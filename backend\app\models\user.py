"""
User and Authentication Database Models
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy import Column, Integer, String, DateTime, JSON, Text, Boolean
from sqlalchemy.orm import relationship
from pydantic import BaseModel, validator, EmailStr
from enum import Enum

from .base import BaseDBModel, BaseSchema, BaseCreateSchema, BaseUpdateSchema, BaseResponseSchema


class UserRole(str, Enum):
    """User roles"""
    ADMIN = "admin"
    ANALYST = "analyst"
    VIEWER = "viewer"
    API_USER = "api_user"


class UserStatus(str, Enum):
    """User status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"


class User(BaseDBModel):
    """User database model"""
    
    __tablename__ = "users"
    
    # Core user fields
    username = Column(String(100), nullable=False, unique=True, index=True)
    email = Column(String(255), nullable=False, unique=True, index=True)
    full_name = Column(String(255), nullable=True)
    hashed_password = Column(String(255), nullable=False)
    
    # User status and role
    role = Column(String(50), default="viewer", nullable=False, index=True)
    status = Column(String(50), default="active", nullable=False, index=True)
    
    # Authentication settings
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    email_verified = Column(Boolean, default=False, nullable=False)
    
    # Security settings
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime, nullable=True)
    password_changed_at = Column(DateTime, nullable=True)
    last_login = Column(DateTime, nullable=True)
    
    # Profile information
    department = Column(String(100), nullable=True)
    organization = Column(String(255), nullable=True)
    phone = Column(String(50), nullable=True)
    timezone = Column(String(50), default="UTC", nullable=False)
    
    # Preferences
    preferences = Column(JSON, default=dict, nullable=False)
    permissions = Column(JSON, default=list, nullable=False)  # Custom permissions
    
    # API access
    api_key = Column(String(255), nullable=True, unique=True, index=True)
    api_key_created_at = Column(DateTime, nullable=True)
    api_key_last_used = Column(DateTime, nullable=True)
    
    # Audit fields
    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"


class UserSession(BaseDBModel):
    """User Session database model"""
    
    __tablename__ = "user_sessions"
    
    # Session identification
    session_id = Column(String(255), nullable=False, unique=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)  # Foreign key
    
    # Session details
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(Text, nullable=True)
    device_info = Column(JSON, default=dict, nullable=False)
    
    # Session lifecycle
    expires_at = Column(DateTime, nullable=False)
    last_activity = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Security tracking
    login_method = Column(String(50), nullable=True)  # password, api_key, sso
    location = Column(JSON, default=dict, nullable=False)  # Geolocation data
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, active={self.is_active})>"


class UserActivity(BaseDBModel):
    """User Activity Log database model"""
    
    __tablename__ = "user_activities"
    
    # Activity identification
    user_id = Column(Integer, nullable=False, index=True)  # Foreign key
    session_id = Column(String(255), nullable=True, index=True)
    
    # Activity details
    action = Column(String(100), nullable=False, index=True)
    resource = Column(String(255), nullable=True, index=True)
    resource_id = Column(String(100), nullable=True, index=True)
    
    # Request details
    method = Column(String(10), nullable=True)  # HTTP method
    endpoint = Column(String(500), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # Result and metadata
    status_code = Column(Integer, nullable=True)
    success = Column(Boolean, default=True, nullable=False)
    error_message = Column(Text, nullable=True)
    metadata = Column(JSON, default=dict, nullable=False)
    
    # Performance
    duration_ms = Column(Integer, nullable=True)
    
    def __repr__(self):
        return f"<UserActivity(id={self.id}, user_id={self.user_id}, action='{self.action}')>"


# Pydantic schemas for API
class UserBase(BaseSchema):
    """Base user schema"""
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    role: UserRole = UserRole.VIEWER
    status: UserStatus = UserStatus.ACTIVE
    department: Optional[str] = None
    organization: Optional[str] = None
    phone: Optional[str] = None
    timezone: str = "UTC"
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('Username must be at least 3 characters long')
        if not v.isalnum() and '_' not in v and '-' not in v:
            raise ValueError('Username can only contain letters, numbers, underscores, and hyphens')
        return v


class UserCreate(UserBase, BaseCreateSchema):
    """Schema for creating users"""
    password: str
    confirm_password: str
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'password' in values and v != values['password']:
            raise ValueError('Passwords do not match')
        return v


class UserUpdate(BaseUpdateSchema):
    """Schema for updating users"""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = None
    status: Optional[UserStatus] = None
    department: Optional[str] = None
    organization: Optional[str] = None
    phone: Optional[str] = None
    timezone: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None
    permissions: Optional[List[str]] = None


class UserResponse(UserBase, BaseResponseSchema):
    """Schema for user responses"""
    is_active: bool = True
    is_verified: bool = False
    email_verified: bool = False
    last_login: Optional[datetime] = None
    password_changed_at: Optional[datetime] = None
    api_key_created_at: Optional[datetime] = None
    api_key_last_used: Optional[datetime] = None
    preferences: Dict[str, Any] = {}
    permissions: List[str] = []
    created_by: Optional[str] = None


class UserLogin(BaseModel):
    """Schema for user login"""
    username: str
    password: str
    remember_me: bool = False


class UserPasswordChange(BaseModel):
    """Schema for password change"""
    current_password: str
    new_password: str
    confirm_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v


class UserSessionResponse(BaseSchema):
    """Schema for user session responses"""
    session_id: str
    user_id: int
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    expires_at: datetime
    last_activity: datetime
    is_active: bool = True
    login_method: Optional[str] = None
    location: Dict[str, Any] = {}
    created_at: datetime


class UserActivityResponse(BaseSchema):
    """Schema for user activity responses"""
    user_id: int
    action: str
    resource: Optional[str] = None
    resource_id: Optional[str] = None
    method: Optional[str] = None
    endpoint: Optional[str] = None
    ip_address: Optional[str] = None
    status_code: Optional[int] = None
    success: bool = True
    error_message: Optional[str] = None
    duration_ms: Optional[int] = None
    created_at: datetime


class UserStatistics(BaseModel):
    """User statistics schema"""
    total_users: int
    active_users: int
    inactive_users: int
    users_by_role: Dict[str, int]
    users_by_status: Dict[str, int]
    recent_registrations: int  # Last 30 days
    recent_logins: int  # Last 24 hours
    average_session_duration: Optional[float] = None  # In minutes
    top_active_users: List[Dict[str, Any]]
    login_attempts_last_24h: int
    failed_login_attempts_last_24h: int


class APIKeyResponse(BaseModel):
    """Schema for API key responses"""
    api_key: str
    created_at: datetime
    last_used: Optional[datetime] = None
    expires_at: Optional[datetime] = None


class TokenResponse(BaseModel):
    """Schema for authentication token responses"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    refresh_token: Optional[str] = None
    user: UserResponse
