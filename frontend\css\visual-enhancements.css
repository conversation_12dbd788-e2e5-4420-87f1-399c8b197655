/* Visual Enhancements - Charts, Graphs, and Advanced Components */

/* Theme-aware color variables for visualizations */
:root {
    --viz-bg-primary: rgba(255, 255, 255, 0.1);
    --viz-bg-secondary: #f8f9fa;
    --viz-text-primary: #333;
    --viz-primary-color: #007bff;
    --viz-primary-light: #66b3ff;
    --viz-danger-color: #dc2626;
    --viz-danger-light: #ef4444;
    --viz-success-color: #059669;
    --viz-warning-color: #d97706;
}

/* Dark theme visualization colors */
body.theme-dark-professional {
    --viz-bg-primary: rgba(51, 65, 85, 0.3);
    --viz-bg-secondary: #334155;
    --viz-text-primary: #f8fafc;
    --viz-primary-color: #60a5fa;
    --viz-primary-light: #93c5fd;
    --viz-danger-color: #f87171;
    --viz-danger-light: #fca5a5;
    --viz-success-color: #10b981;
    --viz-warning-color: #f59e0b;
}

/* Animated Progress Rings */
.progress-ring {
    width: 120px;
    height: 120px;
    position: relative;
    margin: 0 auto;
}

.progress-ring svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.progress-ring circle {
    fill: none;
    stroke-width: 8;
    stroke-linecap: round;
    transition: stroke-dasharray 0.5s ease;
}

.progress-ring .bg-circle {
    stroke: var(--viz-bg-primary);
}

.progress-ring .progress-circle {
    stroke: var(--viz-primary-color);
    stroke-dasharray: 0 283;
    animation: progress-fill 2s ease-out;
}

@keyframes progress-fill {
    from { stroke-dasharray: 0 283; }
}

.progress-ring .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--viz-text-primary);
}

/* Animated Counters */
.animated-counter {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--viz-primary-color);
    display: inline-block;
    min-width: 100px;
    text-align: center;
}

.animated-counter.counting {
    animation: counter-glow 0.5s ease-out;
}

@keyframes counter-glow {
    0% { text-shadow: 0 0 5px currentColor; }
    50% { text-shadow: 0 0 20px currentColor, 0 0 30px currentColor; }
    100% { text-shadow: none; }
}

/* Threat Level Indicators */
.threat-level {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.threat-level::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: threat-scan 3s ease-in-out infinite;
}

@keyframes threat-scan {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

.threat-level.low {
    background: linear-gradient(135deg, var(--viz-success-color) 0%, #059669 100%);
    color: white;
}

.threat-level.medium {
    background: linear-gradient(135deg, var(--viz-warning-color) 0%, #d97706 100%);
    color: white;
}

.threat-level.high {
    background: linear-gradient(135deg, var(--viz-danger-light) 0%, var(--viz-danger-color) 100%);
    color: white;
}

.threat-level.critical {
    background: linear-gradient(135deg, #7c2d12 0%, #991b1b 100%);
    color: white;
    animation: critical-pulse 1s ease-in-out infinite alternate;
}

@keyframes critical-pulse {
    from { box-shadow: 0 0 10px rgba(248, 113, 113, 0.5); }
    to { box-shadow: 0 0 30px rgba(248, 113, 113, 0.8), 0 0 40px rgba(248, 113, 113, 0.3); }
}

/* Dark theme critical pulse adjustments */
body.theme-dark-professional .threat-level.critical {
    animation: critical-pulse-dark 1s ease-in-out infinite alternate;
}

@keyframes critical-pulse-dark {
    from { box-shadow: 0 0 10px rgba(248, 113, 113, 0.6); }
    to { box-shadow: 0 0 30px rgba(248, 113, 113, 0.9), 0 0 40px rgba(248, 113, 113, 0.4); }
}

/* Network Topology Visualization */
.network-node {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--viz-primary-color) 0%, var(--viz-primary-light) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(96, 165, 250, 0.3);
}

.network-node:hover {
    transform: scale(1.2);
    box-shadow: 0 8px 25px rgba(96, 165, 250, 0.5);
}

/* Dark theme network node adjustments */
body.theme-dark-professional .network-node {
    box-shadow: 0 4px 15px rgba(96, 165, 250, 0.4);
}

body.theme-dark-professional .network-node:hover {
    box-shadow: 0 8px 25px rgba(96, 165, 250, 0.6);
}

.network-node.compromised {
    background: linear-gradient(135deg, var(--viz-danger-color) 0%, var(--viz-danger-light) 100%);
    animation: node-alert 1s ease-in-out infinite alternate;
}

@keyframes node-alert {
    from { box-shadow: 0 4px 15px rgba(248, 113, 113, 0.3); }
    to { box-shadow: 0 8px 30px rgba(248, 113, 113, 0.7), 0 0 40px rgba(248, 113, 113, 0.3); }
}

/* Dark theme compromised node adjustments */
body.theme-dark-professional .network-node.compromised {
    animation: node-alert-dark 1s ease-in-out infinite alternate;
}

@keyframes node-alert-dark {
    from { box-shadow: 0 4px 15px rgba(248, 113, 113, 0.4); }
    to { box-shadow: 0 8px 30px rgba(248, 113, 113, 0.8), 0 0 40px rgba(248, 113, 113, 0.4); }
}

.network-connection {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, var(--viz-primary-color), transparent);
    transform-origin: left center;
    animation: data-flow 2s linear infinite;
}

@keyframes data-flow {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
}

/* Heatmap Grid */
.heatmap-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(20px, 1fr));
    gap: 2px;
    padding: 1rem;
    background: var(--viz-bg-secondary);
    border-radius: 8px;
}

.heatmap-cell {
    aspect-ratio: 1;
    border-radius: 2px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.heatmap-cell:hover {
    transform: scale(1.2);
    z-index: 10;
}

/* Light theme heatmap colors */
.heatmap-cell.intensity-0 { background: rgba(96, 165, 250, 0.1); }
.heatmap-cell.intensity-1 { background: rgba(96, 165, 250, 0.3); }
.heatmap-cell.intensity-2 { background: rgba(96, 165, 250, 0.5); }
.heatmap-cell.intensity-3 { background: rgba(96, 165, 250, 0.7); }
.heatmap-cell.intensity-4 { background: rgba(96, 165, 250, 0.9); }

/* Dark theme heatmap colors */
body.theme-dark-professional .heatmap-cell.intensity-0 { background: rgba(96, 165, 250, 0.15); }
body.theme-dark-professional .heatmap-cell.intensity-1 { background: rgba(96, 165, 250, 0.35); }
body.theme-dark-professional .heatmap-cell.intensity-2 { background: rgba(96, 165, 250, 0.55); }
body.theme-dark-professional .heatmap-cell.intensity-3 { background: rgba(96, 165, 250, 0.75); }
body.theme-dark-professional .heatmap-cell.intensity-4 { background: rgba(96, 165, 250, 0.95); }

/* Timeline Visualization */
.timeline-chart {
    position: relative;
    height: 200px;
    background: linear-gradient(to right,
        rgba(96, 165, 250, 0.1) 0%,
        rgba(96, 165, 250, 0.05) 50%,
        rgba(96, 165, 250, 0.1) 100%);
    border-radius: 8px;
    overflow: hidden;
}

/* Dark theme timeline background */
body.theme-dark-professional .timeline-chart {
    background: linear-gradient(to right,
        rgba(96, 165, 250, 0.15) 0%,
        rgba(96, 165, 250, 0.08) 50%,
        rgba(96, 165, 250, 0.15) 100%);
}

.timeline-bar {
    position: absolute;
    bottom: 0;
    width: 20px;
    background: linear-gradient(to top, var(--viz-primary-color), var(--viz-primary-light));
    border-radius: 4px 4px 0 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.timeline-bar:hover {
    transform: scaleY(1.1);
    box-shadow: 0 0 15px rgba(96, 165, 250, 0.5);
}

.timeline-bar.high-activity {
    background: linear-gradient(to top, var(--viz-danger-color), var(--viz-danger-light));
    animation: bar-pulse 2s ease-in-out infinite alternate;
}

@keyframes bar-pulse {
    from { opacity: 0.8; }
    to { opacity: 1; }
}

/* Radar Chart */
.radar-chart {
    width: 200px;
    height: 200px;
    position: relative;
    margin: 0 auto;
}

.radar-chart svg {
    width: 100%;
    height: 100%;
}

.radar-grid {
    fill: none;
    stroke: rgba(255, 255, 255, 0.2);
    stroke-width: 1;
}

.radar-area {
    fill: rgba(0, 123, 255, 0.3);
    stroke: var(--primary-color, #007bff);
    stroke-width: 2;
    animation: radar-sweep 3s ease-in-out infinite;
}

@keyframes radar-sweep {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* Status Indicators with Animations */
.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
    position: relative;
}

.status-dot.online {
    background: #10b981;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
    animation: online-pulse 2s ease-in-out infinite;
}

@keyframes online-pulse {
    0%, 100% { box-shadow: 0 0 10px rgba(16, 185, 129, 0.5); }
    50% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.8), 0 0 30px rgba(16, 185, 129, 0.3); }
}

.status-dot.offline {
    background: #ef4444;
    animation: offline-blink 1s ease-in-out infinite;
}

@keyframes offline-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

.status-dot.warning {
    background: #f59e0b;
    animation: warning-flash 0.5s ease-in-out infinite alternate;
}

@keyframes warning-flash {
    from { background: #f59e0b; }
    to { background: #fbbf24; }
}

/* Data Flow Animation */
.data-stream {
    position: relative;
    height: 4px;
    background: rgba(0, 123, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
}

.data-stream::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        var(--primary-color, #007bff), 
        transparent);
    animation: data-flow-stream 2s linear infinite;
}

@keyframes data-flow-stream {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Particle System Background */
.particle-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.3;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color, #007bff);
    border-radius: 50%;
    animation: particle-float 10s linear infinite;
}

@keyframes particle-float {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .progress-ring {
        width: 80px;
        height: 80px;
    }
    
    .animated-counter {
        font-size: 1.8rem;
    }
    
    .network-node {
        width: 40px;
        height: 40px;
        font-size: 0.8rem;
    }
    
    .radar-chart {
        width: 150px;
        height: 150px;
    }
}
