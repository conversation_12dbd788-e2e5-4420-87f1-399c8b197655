"""
Base database models for CTI Dashboard
"""

from datetime import datetime
from typing import Any, Dict, Optional
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, JSON
from sqlalchemy.ext.declarative import declarative_base, declared_attr
from sqlalchemy.orm import Session
from pydantic import BaseModel


# SQLAlchemy base
Base = declarative_base()


class TimestampMixin:
    """Mixin for created_at and updated_at timestamps"""
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class BaseDBModel(Base, TimestampMixin):
    """Base model with common fields"""
    
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
    
    @declared_attr
    def __tablename__(cls):
        return cls.__name__.lower()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """Update model from dictionary"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)


class AuditMixin:
    """Mixin for audit fields"""
    
    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    metadata = Column(JSON, nullable=True)


# Pydantic base models for API schemas
class BaseSchema(BaseModel):
    """Base Pydantic schema"""
    
    class Config:
        orm_mode = True
        validate_assignment = True
        use_enum_values = True


class TimestampSchema(BaseSchema):
    """Schema with timestamp fields"""
    
    created_at: datetime
    updated_at: datetime


class BaseCreateSchema(BaseSchema):
    """Base schema for create operations"""
    pass


class BaseUpdateSchema(BaseSchema):
    """Base schema for update operations"""
    pass


class BaseResponseSchema(TimestampSchema):
    """Base schema for API responses"""
    
    id: int


# Database session dependency
class DatabaseManager:
    """Database session manager"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def get_by_id(self, model_class, id: int):
        """Get record by ID"""
        return self.session.query(model_class).filter(model_class.id == id).first()
    
    def get_by_field(self, model_class, field_name: str, value: Any):
        """Get record by field value"""
        return self.session.query(model_class).filter(
            getattr(model_class, field_name) == value
        ).first()
    
    def get_all(self, model_class, skip: int = 0, limit: int = 100):
        """Get all records with pagination"""
        return self.session.query(model_class).offset(skip).limit(limit).all()
    
    def create(self, model_class, **kwargs):
        """Create new record"""
        db_obj = model_class(**kwargs)
        self.session.add(db_obj)
        self.session.commit()
        self.session.refresh(db_obj)
        return db_obj
    
    def update(self, db_obj, update_data: Dict[str, Any]):
        """Update existing record"""
        for field, value in update_data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
        
        self.session.commit()
        self.session.refresh(db_obj)
        return db_obj
    
    def delete(self, db_obj):
        """Delete record"""
        self.session.delete(db_obj)
        self.session.commit()
        return True
    
    def soft_delete(self, db_obj):
        """Soft delete record (set is_active = False)"""
        if hasattr(db_obj, 'is_active'):
            db_obj.is_active = False
            self.session.commit()
            return True
        return False


# Repository pattern base class
class BaseRepository:
    """Base repository class"""
    
    def __init__(self, session: Session, model_class):
        self.session = session
        self.model_class = model_class
        self.db_manager = DatabaseManager(session)
    
    def get(self, id: int):
        """Get by ID"""
        return self.db_manager.get_by_id(self.model_class, id)
    
    def get_by_field(self, field_name: str, value: Any):
        """Get by field value"""
        return self.db_manager.get_by_field(self.model_class, field_name, value)
    
    def get_multi(self, skip: int = 0, limit: int = 100):
        """Get multiple records"""
        return self.db_manager.get_all(self.model_class, skip, limit)
    
    def create(self, obj_in: BaseCreateSchema):
        """Create new record"""
        obj_data = obj_in.dict()
        return self.db_manager.create(self.model_class, **obj_data)
    
    def update(self, db_obj, obj_in: BaseUpdateSchema):
        """Update existing record"""
        update_data = obj_in.dict(exclude_unset=True)
        return self.db_manager.update(db_obj, update_data)
    
    def delete(self, id: int):
        """Delete record"""
        db_obj = self.get(id)
        if db_obj:
            return self.db_manager.delete(db_obj)
        return False
    
    def soft_delete(self, id: int):
        """Soft delete record"""
        db_obj = self.get(id)
        if db_obj:
            return self.db_manager.soft_delete(db_obj)
        return False


# Database utilities
def create_tables(engine):
    """Create all database tables"""
    Base.metadata.create_all(bind=engine)


def drop_tables(engine):
    """Drop all database tables"""
    Base.metadata.drop_all(bind=engine)


# Search and filtering utilities
class SearchFilter:
    """Utility class for building search filters"""
    
    def __init__(self, model_class):
        self.model_class = model_class
        self.filters = []
    
    def add_text_filter(self, field_name: str, value: str, exact: bool = False):
        """Add text search filter"""
        field = getattr(self.model_class, field_name)
        if exact:
            self.filters.append(field == value)
        else:
            self.filters.append(field.ilike(f"%{value}%"))
        return self
    
    def add_date_range_filter(self, field_name: str, start_date: datetime, end_date: datetime):
        """Add date range filter"""
        field = getattr(self.model_class, field_name)
        self.filters.append(field >= start_date)
        self.filters.append(field <= end_date)
        return self
    
    def add_in_filter(self, field_name: str, values: list):
        """Add 'in' filter"""
        field = getattr(self.model_class, field_name)
        self.filters.append(field.in_(values))
        return self
    
    def apply_to_query(self, query):
        """Apply filters to query"""
        for filter_condition in self.filters:
            query = query.filter(filter_condition)
        return query


# Pagination utility
class PaginationParams(BaseModel):
    """Pagination parameters"""
    
    page: int = 1
    size: int = 50
    
    @property
    def offset(self) -> int:
        return (self.page - 1) * self.size
    
    @property
    def limit(self) -> int:
        return self.size


class PaginatedResponse(BaseModel):
    """Paginated response model"""
    
    items: list
    total: int
    page: int
    size: int
    pages: int
    
    @classmethod
    def create(cls, items: list, total: int, pagination: PaginationParams):
        """Create paginated response"""
        pages = (total + pagination.size - 1) // pagination.size
        return cls(
            items=items,
            total=total,
            page=pagination.page,
            size=pagination.size,
            pages=pages
        )
