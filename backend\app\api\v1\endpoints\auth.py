"""
Authentication API endpoints
"""

import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ....config.database import get_db
from ....config.settings import get_settings
from ....services.auth_service import AuthService, get_auth_service
from ....models.user import UserLogin, UserCreate, UserPasswordChange, TokenResponse, UserResponse
from ....core.exceptions import AuthenticationError, ValidationError
from ....core.security import get_current_active_user

logger = logging.getLogger(__name__)
settings = get_settings()

# Create router
router = APIRouter()

# Security scheme
security = HTTPBearer()


class LoginResponse(BaseModel):
    """Login response schema"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse


class RegisterResponse(BaseModel):
    """Registration response schema"""
    message: str
    user: UserResponse


class APIKeyResponse(BaseModel):
    """API key response schema"""
    api_key: str
    message: str


# Authentication endpoints
@router.post("/login", response_model=LoginResponse)
async def login(
    user_credentials: UserLogin,
    request: Request,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Authenticate user and return access token"""
    try:
        # Get client info
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        # Authenticate user
        user = auth_service.authenticate_user(
            username=user_credentials.username,
            password=user_credentials.password,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = auth_service.create_access_token(
            user=user,
            expires_delta=access_token_expires
        )
        
        # Create session
        session = auth_service.create_session(
            user=user,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        logger.info(f"User logged in: {user.username}")
        
        return LoginResponse(
            access_token=access_token,
            expires_in=settings.access_token_expire_minutes * 60,
            user=UserResponse.from_orm(user)
        )
        
    except AuthenticationError as e:
        logger.warning(f"Authentication failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during login"
        )


@router.post("/register", response_model=RegisterResponse)
async def register(
    user_data: UserCreate,
    request: Request,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Register a new user"""
    try:
        # Create user
        user = auth_service.create_user(user_data)
        
        logger.info(f"User registered: {user.username}")
        
        return RegisterResponse(
            message="User registered successfully",
            user=UserResponse.from_orm(user)
        )
        
    except AuthenticationError as e:
        logger.warning(f"Registration failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ValidationError as e:
        logger.warning(f"Registration validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during registration"
        )


@router.post("/logout")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Logout user and invalidate session"""
    try:
        # For now, we'll just return success since JWT tokens are stateless
        # In a production system, you might want to maintain a blacklist of tokens
        
        logger.info("User logged out")
        
        return {
            "message": "Successfully logged out"
        }
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during logout"
        )


@router.post("/change-password")
async def change_password(
    password_data: UserPasswordChange,
    current_user=Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Change user password"""
    try:
        # Get user from database
        user = auth_service.db.query(auth_service.db.query(User).filter(User.username == current_user.username).first())
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Change password
        success = auth_service.change_password(
            user=user,
            current_password=password_data.current_password,
            new_password=password_data.new_password
        )
        
        if success:
            return {"message": "Password changed successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to change password")
            
    except AuthenticationError as e:
        logger.warning(f"Password change failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Password change error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during password change"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user=Depends(get_current_active_user)
):
    """Get current user information"""
    try:
        return UserResponse.from_orm(current_user)
        
    except Exception as e:
        logger.error(f"Get user info error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/api-key", response_model=APIKeyResponse)
async def generate_api_key(
    current_user=Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Generate API key for current user"""
    try:
        # Get user from database
        user = auth_service.db.query(User).filter(User.username == current_user.username).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Generate API key
        api_key = auth_service.generate_api_key(user)
        
        return APIKeyResponse(
            api_key=api_key,
            message="API key generated successfully"
        )
        
    except Exception as e:
        logger.error(f"API key generation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during API key generation"
        )


@router.delete("/api-key")
async def revoke_api_key(
    current_user=Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Revoke API key for current user"""
    try:
        # Get user from database
        user = auth_service.db.query(User).filter(User.username == current_user.username).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Revoke API key
        user.api_key = None
        user.api_key_created_at = None
        auth_service.db.commit()
        
        auth_service.log_activity(user.id, "api_key_revoked")
        
        return {"message": "API key revoked successfully"}
        
    except Exception as e:
        logger.error(f"API key revocation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during API key revocation"
        )


@router.get("/sessions")
async def get_user_sessions(
    current_user=Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Get active sessions for current user"""
    try:
        # Get user sessions
        sessions = auth_service.db.query(UserSession).filter(
            UserSession.user_id == current_user.id,
            UserSession.is_active == True
        ).order_by(UserSession.last_activity.desc()).all()
        
        return {
            "sessions": [
                {
                    "session_id": session.session_id,
                    "ip_address": session.ip_address,
                    "user_agent": session.user_agent,
                    "last_activity": session.last_activity.isoformat(),
                    "expires_at": session.expires_at.isoformat(),
                    "login_method": session.login_method
                }
                for session in sessions
            ]
        }
        
    except Exception as e:
        logger.error(f"Get sessions error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete("/sessions/{session_id}")
async def revoke_session(
    session_id: str,
    current_user=Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Revoke a specific session"""
    try:
        # Check if session belongs to current user
        session = auth_service.db.query(UserSession).filter(
            UserSession.session_id == session_id,
            UserSession.user_id == current_user.id
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Invalidate session
        success = auth_service.invalidate_session(session_id)
        
        if success:
            return {"message": "Session revoked successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to revoke session")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Session revocation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/verify-token")
async def verify_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Verify JWT token"""
    try:
        token = credentials.credentials
        token_data = auth_service.verify_token(token)
        
        return {
            "valid": True,
            "token_data": token_data
        }
        
    except AuthenticationError as e:
        return {
            "valid": False,
            "error": str(e)
        }
    except Exception as e:
        logger.error(f"Token verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
