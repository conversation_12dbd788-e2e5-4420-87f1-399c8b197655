"""
Permission and authorization utilities
"""

import logging
from typing import List, Dict, Any, Optional
from functools import wraps
from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session

from ..models.user import User, UserRole
from ..config.database import get_db
from .security import get_current_active_user

logger = logging.getLogger(__name__)


class Permission:
    """Permission constants"""
    
    # IoC permissions
    IOC_READ = "ioc:read"
    IOC_WRITE = "ioc:write"
    IOC_DELETE = "ioc:delete"
    
    # Threat Actor permissions
    ACTOR_READ = "actor:read"
    ACTOR_ANALYZE = "actor:analyze"
    ACTOR_REPORT = "actor:report"
    
    # Passive Scanning permissions
    PASSIVE_SCAN = "passive:scan"
    PASSIVE_READ = "passive:read"
    
    # Watchlist permissions
    WATCHLIST_READ = "watchlist:read"
    WATCHLIST_WRITE = "watchlist:write"
    WATCHLIST_CHECK = "watchlist:check"
    
    # System permissions
    SYSTEM_READ = "system:read"
    SYSTEM_CONFIG_READ = "system:config:read"
    SYSTEM_CONFIG_WRITE = "system:config:write"
    SYSTEM_API_KEYS_READ = "system:api-keys:read"
    SYSTEM_API_KEYS_WRITE = "system:api-keys:write"
    SYSTEM_MAINTENANCE = "system:maintenance"
    
    # User management permissions
    USER_READ = "user:read"
    USER_WRITE = "user:write"
    USER_DELETE = "user:delete"
    
    # Admin permissions
    ADMIN_ALL = "admin:all"


class RolePermissions:
    """Role-based permission mappings"""
    
    ADMIN = [
        Permission.IOC_READ, Permission.IOC_WRITE, Permission.IOC_DELETE,
        Permission.ACTOR_READ, Permission.ACTOR_ANALYZE, Permission.ACTOR_REPORT,
        Permission.PASSIVE_SCAN, Permission.PASSIVE_READ,
        Permission.WATCHLIST_READ, Permission.WATCHLIST_WRITE, Permission.WATCHLIST_CHECK,
        Permission.SYSTEM_READ, Permission.SYSTEM_CONFIG_READ, Permission.SYSTEM_CONFIG_WRITE,
        Permission.SYSTEM_API_KEYS_READ, Permission.SYSTEM_API_KEYS_WRITE,
        Permission.SYSTEM_MAINTENANCE,
        Permission.USER_READ, Permission.USER_WRITE, Permission.USER_DELETE,
        Permission.ADMIN_ALL
    ]
    
    ANALYST = [
        Permission.IOC_READ, Permission.IOC_WRITE,
        Permission.ACTOR_READ, Permission.ACTOR_ANALYZE, Permission.ACTOR_REPORT,
        Permission.PASSIVE_SCAN, Permission.PASSIVE_READ,
        Permission.WATCHLIST_READ, Permission.WATCHLIST_WRITE, Permission.WATCHLIST_CHECK,
        Permission.SYSTEM_READ
    ]
    
    VIEWER = [
        Permission.IOC_READ,
        Permission.ACTOR_READ,
        Permission.PASSIVE_READ,
        Permission.WATCHLIST_READ,
        Permission.SYSTEM_READ
    ]
    
    API_USER = [
        Permission.IOC_READ, Permission.IOC_WRITE,
        Permission.ACTOR_READ, Permission.ACTOR_ANALYZE,
        Permission.PASSIVE_SCAN, Permission.PASSIVE_READ,
        Permission.WATCHLIST_CHECK
    ]


class PermissionChecker:
    """Permission checking utilities"""
    
    @staticmethod
    def get_role_permissions(role: UserRole) -> List[str]:
        """Get permissions for a role"""
        role_map = {
            UserRole.ADMIN: RolePermissions.ADMIN,
            UserRole.ANALYST: RolePermissions.ANALYST,
            UserRole.VIEWER: RolePermissions.VIEWER,
            UserRole.API_USER: RolePermissions.API_USER
        }
        return role_map.get(role, [])
    
    @staticmethod
    def get_user_permissions(user: User) -> List[str]:
        """Get all permissions for a user (role + custom)"""
        role_permissions = PermissionChecker.get_role_permissions(UserRole(user.role))
        custom_permissions = user.permissions or []
        
        # Combine and deduplicate
        all_permissions = list(set(role_permissions + custom_permissions))
        return all_permissions
    
    @staticmethod
    def has_permission(user: User, permission: str) -> bool:
        """Check if user has a specific permission"""
        user_permissions = PermissionChecker.get_user_permissions(user)
        
        # Admin has all permissions
        if Permission.ADMIN_ALL in user_permissions:
            return True
        
        return permission in user_permissions
    
    @staticmethod
    def has_any_permission(user: User, permissions: List[str]) -> bool:
        """Check if user has any of the specified permissions"""
        return any(PermissionChecker.has_permission(user, perm) for perm in permissions)
    
    @staticmethod
    def has_all_permissions(user: User, permissions: List[str]) -> bool:
        """Check if user has all of the specified permissions"""
        return all(PermissionChecker.has_permission(user, perm) for perm in permissions)
    
    @staticmethod
    def can_access_resource(user: User, resource_type: str, action: str) -> bool:
        """Check if user can perform action on resource type"""
        permission = f"{resource_type}:{action}"
        return PermissionChecker.has_permission(user, permission)


def require_permission(permission: str):
    """Decorator to require specific permission"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get current user from kwargs or dependencies
            current_user = kwargs.get('current_user')
            if not current_user:
                # Try to get from dependencies
                for arg in args:
                    if isinstance(arg, User):
                        current_user = arg
                        break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if not PermissionChecker.has_permission(current_user, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission required: {permission}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_any_permission(permissions: List[str]):
    """Decorator to require any of the specified permissions"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user:
                for arg in args:
                    if isinstance(arg, User):
                        current_user = arg
                        break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if not PermissionChecker.has_any_permission(current_user, permissions):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"One of these permissions required: {', '.join(permissions)}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_role(role: UserRole):
    """Decorator to require specific role"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user:
                for arg in args:
                    if isinstance(arg, User):
                        current_user = arg
                        break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if current_user.role != role.value:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Role required: {role.value}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


class RequirePermission:
    """FastAPI dependency for permission checking"""
    
    def __init__(self, permission: str):
        self.permission = permission
    
    def __call__(self, current_user: User = Depends(get_current_active_user)):
        if not PermissionChecker.has_permission(current_user, self.permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission required: {self.permission}"
            )
        return current_user


class RequireAnyPermission:
    """FastAPI dependency for checking any of multiple permissions"""
    
    def __init__(self, permissions: List[str]):
        self.permissions = permissions
    
    def __call__(self, current_user: User = Depends(get_current_active_user)):
        if not PermissionChecker.has_any_permission(current_user, self.permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"One of these permissions required: {', '.join(self.permissions)}"
            )
        return current_user


class RequireRole:
    """FastAPI dependency for role checking"""
    
    def __init__(self, role: UserRole):
        self.role = role
    
    def __call__(self, current_user: User = Depends(get_current_active_user)):
        if current_user.role != self.role.value:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role required: {self.role.value}"
            )
        return current_user


# Resource-based access control
class ResourceAccessControl:
    """Resource-based access control utilities"""
    
    @staticmethod
    def can_access_ioc(user: User, ioc_id: int, action: str, db: Session) -> bool:
        """Check if user can access specific IoC"""
        # Basic permission check
        permission = f"ioc:{action}"
        if not PermissionChecker.has_permission(user, permission):
            return False
        
        # Additional resource-specific checks can be added here
        # For example, check if user created the IoC, or if it's in their organization
        
        return True
    
    @staticmethod
    def can_access_actor(user: User, actor_id: int, action: str, db: Session) -> bool:
        """Check if user can access specific threat actor"""
        permission = f"actor:{action}"
        if not PermissionChecker.has_permission(user, permission):
            return False
        
        return True
    
    @staticmethod
    def can_access_watchlist_item(user: User, item_id: int, action: str, db: Session) -> bool:
        """Check if user can access specific watchlist item"""
        permission = f"watchlist:{action}"
        if not PermissionChecker.has_permission(user, permission):
            return False
        
        return True


# Permission audit utilities
class PermissionAudit:
    """Permission auditing utilities"""
    
    @staticmethod
    def log_permission_check(user: User, permission: str, granted: bool, resource: str = None):
        """Log permission check for auditing"""
        logger.info(
            f"Permission check: user={user.username}, permission={permission}, "
            f"granted={granted}, resource={resource}"
        )
    
    @staticmethod
    def get_user_permission_summary(user: User) -> Dict[str, Any]:
        """Get summary of user permissions"""
        permissions = PermissionChecker.get_user_permissions(user)
        
        return {
            "user_id": user.id,
            "username": user.username,
            "role": user.role,
            "total_permissions": len(permissions),
            "permissions": permissions,
            "is_admin": Permission.ADMIN_ALL in permissions,
            "can_manage_users": PermissionChecker.has_permission(user, Permission.USER_WRITE),
            "can_manage_system": PermissionChecker.has_permission(user, Permission.SYSTEM_CONFIG_WRITE)
        }
