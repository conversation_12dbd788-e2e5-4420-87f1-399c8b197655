"""
Passive Scan Module - Collect passive intelligence from Shodan, Censys, ZoomEye
"""

import asyncio
import base64
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from urllib.parse import quote

import aiohttp
from pydantic import BaseModel


@dataclass
class PassiveScanResult:
    """Passive scan result data model"""
    target: str
    scan_type: str  # 'ip', 'domain', 'subnet'
    source: str  # 'shodan', 'censys', 'zoomeye'
    timestamp: datetime
    services: List[Dict[str, Any]]
    vulnerabilities: List[Dict[str, Any]]
    geolocation: Optional[Dict[str, str]] = None
    organization: Optional[str] = None
    asn: Optional[str] = None
    ports: List[int] = None
    banners: List[str] = None
    certificates: List[Dict[str, Any]] = None
    raw_data: Dict[str, Any] = None

    def __post_init__(self):
        if self.ports is None:
            self.ports = []
        if self.banners is None:
            self.banners = []
        if self.certificates is None:
            self.certificates = []
        if self.raw_data is None:
            self.raw_data = {}


class PassiveScanSummary(BaseModel):
    """Summary of passive scan results"""
    target: str
    total_services: int
    critical_vulnerabilities: int
    high_risk_ports: List[int]
    exposed_services: List[str]
    risk_score: float
    recommendations: List[str]
    scan_sources: List[str]


class PassiveScanner:
    """Main passive scanning class integrating multiple intelligence sources"""

    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Scanner service configurations
        self.scanners = {
            'shodan': self._scan_shodan,
            'censys': self._scan_censys,
            'zoomeye': self._scan_zoomeye
        }

        # High-risk ports commonly targeted by attackers
        self.high_risk_ports = [
            21, 22, 23, 25, 53, 80, 110, 135, 139, 143, 443, 445, 993, 995,
            1433, 1521, 3306, 3389, 5432, 5900, 6379, 27017, 50070
        ]

    async def scan_target(self, target: str, scan_type: str = 'auto') -> List[PassiveScanResult]:
        """Perform passive scan on target using all configured sources"""

        if scan_type == 'auto':
            scan_type = self._detect_target_type(target)

        scan_tasks = []
        enabled_sources = self.config.get('enabled_sources', ['shodan', 'censys', 'zoomeye'])

        for source in enabled_sources:
            if source in self.scanners:
                task = self.scanners[source](target, scan_type)
                scan_tasks.append(task)

        # Execute scans concurrently
        results = await asyncio.gather(*scan_tasks, return_exceptions=True)

        # Filter successful results
        scan_results = []
        for result in results:
            if isinstance(result, PassiveScanResult):
                scan_results.append(result)
            elif isinstance(result, Exception):
                self.logger.error(f"Scan error: {result}")

        return scan_results

    def _detect_target_type(self, target: str) -> str:
        """Detect target type (IP, domain, or subnet)"""
        import ipaddress
        import re

        try:
            # Check if it's an IP address
            ipaddress.ip_address(target)
            return 'ip'
        except ValueError:
            pass

        # Check if it's a subnet
        try:
            ipaddress.ip_network(target, strict=False)
            return 'subnet'
        except ValueError:
            pass

        # Check if it's a domain
        if re.match(r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', target):
            return 'domain'

        return 'unknown'

    async def _scan_shodan(self, target: str, scan_type: str) -> PassiveScanResult:
        """Perform passive scan using Shodan API"""

        if not self.config.get('shodan_api_key'):
            raise ValueError("Shodan API key not configured")

        try:
            params = {'key': self.config['shodan_api_key']}

            if scan_type == 'ip':
                url = f"https://api.shodan.io/shodan/host/{target}"
            elif scan_type == 'domain':
                url = f"https://api.shodan.io/dns/domain/{target}"
                params['history'] = 'false'
            else:
                # For subnet scanning, use search API
                url = "https://api.shodan.io/shodan/host/search"
                params['query'] = f"net:{target}"

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_shodan_response(target, data)
                    else:
                        error_data = await response.text()
                        raise Exception(f"Shodan API error {response.status}: {error_data}")

        except Exception as e:
            self.logger.error(f"Shodan scan error for {target}: {e}")
            raise

    def _parse_shodan_response(self, target: str, data: Dict) -> PassiveScanResult:
        """Parse Shodan API response"""

        services = []
        vulnerabilities = []
        ports = []
        banners = []

        # Handle different response formats
        if 'data' in data:  # Host information response
            for service in data.get('data', []):
                port = service.get('port', 0)
                ports.append(port)

                service_info = {
                    'port': port,
                    'protocol': service.get('transport', 'tcp'),
                    'service': service.get('product', 'unknown'),
                    'version': service.get('version', ''),
                    'banner': service.get('data', '').strip()
                }
                services.append(service_info)

                if service_info['banner']:
                    banners.append(service_info['banner'])

                # Extract vulnerabilities
                vulns = service.get('vulns', [])
                for vuln in vulns:
                    vulnerabilities.append({
                        'id': vuln,
                        'port': port,
                        'service': service.get('product', 'unknown'),
                        'severity': 'unknown'  # Shodan doesn't provide severity directly
                    })

        # Extract geolocation and organization info
        geolocation = None
        organization = None
        asn = None

        if 'country_name' in data:
            geolocation = {
                'country': data.get('country_name', ''),
                'city': data.get('city', ''),
                'region': data.get('region_code', ''),
                'latitude': str(data.get('latitude', '')),
                'longitude': str(data.get('longitude', ''))
            }

        if 'org' in data:
            organization = data.get('org', '')

        if 'asn' in data:
            asn = data.get('asn', '')

        return PassiveScanResult(
            target=target,
            scan_type=self._detect_target_type(target),
            source='shodan',
            timestamp=datetime.utcnow(),
            services=services,
            vulnerabilities=vulnerabilities,
            geolocation=geolocation,
            organization=organization,
            asn=asn,
            ports=list(set(ports)),  # Remove duplicates
            banners=banners,
            raw_data=data
        )

    async def _scan_censys(self, target: str, scan_type: str) -> PassiveScanResult:
        """Perform passive scan using Censys API"""

        if not self.config.get('censys_api_id') or not self.config.get('censys_api_secret'):
            raise ValueError("Censys API credentials not configured")

        try:
            # Censys uses basic auth
            auth_string = f"{self.config['censys_api_id']}:{self.config['censys_api_secret']}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Content-Type': 'application/json'
            }

            if scan_type == 'ip':
                url = f"https://search.censys.io/api/v2/hosts/{target}"
            else:
                # For domain/subnet search
                url = "https://search.censys.io/api/v2/hosts/search"
                query = f"ip:{target}" if scan_type == 'subnet' else f"dns.names:{target}"

                payload = {
                    'q': query,
                    'per_page': 100
                }

                async with aiohttp.ClientSession() as session:
                    async with session.post(url, headers=headers, json=payload) as response:
                        if response.status == 200:
                            data = await response.json()
                            # For search results, take the first result
                            if data.get('result', {}).get('hits'):
                                first_hit = data['result']['hits'][0]
                                return self._parse_censys_response(target, first_hit)
                            else:
                                # No results found
                                return PassiveScanResult(
                                    target=target,
                                    scan_type=scan_type,
                                    source='censys',
                                    timestamp=datetime.utcnow(),
                                    services=[],
                                    vulnerabilities=[],
                                    raw_data=data
                                )
                        else:
                            error_data = await response.text()
                            raise Exception(f"Censys API error {response.status}: {error_data}")

            # Direct host lookup
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_censys_response(target, data.get('result', {}))
                    else:
                        error_data = await response.text()
                        raise Exception(f"Censys API error {response.status}: {error_data}")

        except Exception as e:
            self.logger.error(f"Censys scan error for {target}: {e}")
            raise

    def _parse_censys_response(self, target: str, data: Dict) -> PassiveScanResult:
        """Parse Censys API response"""

        services = []
        vulnerabilities = []
        ports = []
        certificates = []

        # Parse services from Censys data
        services_data = data.get('services', [])
        for service in services_data:
            port = service.get('port', 0)
            ports.append(port)

            service_info = {
                'port': port,
                'protocol': service.get('transport_protocol', 'tcp'),
                'service': service.get('service_name', 'unknown'),
                'version': service.get('software', [{}])[0].get('version', '') if service.get('software') else '',
                'banner': service.get('banner', '').strip()
            }
            services.append(service_info)

            # Extract certificate information
            if 'tls' in service and service['tls'].get('certificates'):
                for cert in service['tls']['certificates']:
                    cert_info = {
                        'subject': cert.get('parsed', {}).get('subject_dn', ''),
                        'issuer': cert.get('parsed', {}).get('issuer_dn', ''),
                        'valid_from': cert.get('parsed', {}).get('validity', {}).get('start', ''),
                        'valid_to': cert.get('parsed', {}).get('validity', {}).get('end', ''),
                        'serial_number': cert.get('parsed', {}).get('serial_number', ''),
                        'port': port
                    }
                    certificates.append(cert_info)

        # Extract geolocation
        geolocation = None
        location = data.get('location', {})
        if location:
            geolocation = {
                'country': location.get('country', ''),
                'city': location.get('city', ''),
                'region': location.get('province', ''),
                'latitude': str(location.get('coordinates', {}).get('latitude', '')),
                'longitude': str(location.get('coordinates', {}).get('longitude', ''))
            }

        # Extract organization and ASN
        organization = data.get('autonomous_system', {}).get('name', '')
        asn = str(data.get('autonomous_system', {}).get('asn', ''))

        return PassiveScanResult(
            target=target,
            scan_type=self._detect_target_type(target),
            source='censys',
            timestamp=datetime.utcnow(),
            services=services,
            vulnerabilities=vulnerabilities,
            geolocation=geolocation,
            organization=organization,
            asn=asn,
            ports=list(set(ports)),
            certificates=certificates,
            raw_data=data
        )

    async def _scan_zoomeye(self, target: str, scan_type: str) -> PassiveScanResult:
        """Perform passive scan using ZoomEye API"""

        if not self.config.get('zoomeye_api_key'):
            raise ValueError("ZoomEye API key not configured")

        try:
            headers = {
                'API-KEY': self.config['zoomeye_api_key'],
                'Content-Type': 'application/json'
            }

            if scan_type == 'ip':
                url = f"https://api.zoomeye.org/host/search"
                params = {'query': f'ip:{target}', 'page': 1}
            elif scan_type == 'domain':
                url = f"https://api.zoomeye.org/host/search"
                params = {'query': f'hostname:{target}', 'page': 1}
            else:
                # Subnet search
                url = f"https://api.zoomeye.org/host/search"
                params = {'query': f'cidr:{target}', 'page': 1}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_zoomeye_response(target, data)
                    else:
                        error_data = await response.text()
                        raise Exception(f"ZoomEye API error {response.status}: {error_data}")

        except Exception as e:
            self.logger.error(f"ZoomEye scan error for {target}: {e}")
            raise

    def _parse_zoomeye_response(self, target: str, data: Dict) -> PassiveScanResult:
        """Parse ZoomEye API response"""

        services = []
        vulnerabilities = []
        ports = []
        banners = []

        # ZoomEye returns matches array
        matches = data.get('matches', [])

        geolocation = None
        organization = None
        asn = None

        for match in matches:
            port = match.get('portinfo', {}).get('port', 0)
            if port:
                ports.append(port)

            service_info = {
                'port': port,
                'protocol': match.get('protocol', 'tcp'),
                'service': match.get('portinfo', {}).get('service', 'unknown'),
                'version': match.get('portinfo', {}).get('version', ''),
                'banner': match.get('portinfo', {}).get('banner', '').strip()
            }
            services.append(service_info)

            if service_info['banner']:
                banners.append(service_info['banner'])

            # Extract geolocation from first match
            if not geolocation and match.get('geoinfo'):
                geo = match['geoinfo']
                geolocation = {
                    'country': geo.get('country', {}).get('names', {}).get('en', ''),
                    'city': geo.get('city', {}).get('names', {}).get('en', ''),
                    'region': geo.get('subdivisions', [{}])[0].get('names', {}).get('en', '') if geo.get('subdivisions') else '',
                    'latitude': str(geo.get('location', {}).get('latitude', '')),
                    'longitude': str(geo.get('location', {}).get('longitude', ''))
                }

            # Extract organization info
            if not organization and match.get('rdns'):
                organization = match.get('rdns', '')

        return PassiveScanResult(
            target=target,
            scan_type=self._detect_target_type(target),
            source='zoomeye',
            timestamp=datetime.utcnow(),
            services=services,
            vulnerabilities=vulnerabilities,
            geolocation=geolocation,
            organization=organization,
            asn=asn,
            ports=list(set(ports)),
            banners=banners,
            raw_data=data
        )