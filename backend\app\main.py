"""
Main FastAPI Application Factory - CTI Dashboard
"""

import logging
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from .config.settings import get_settings, validate_configuration
from .api.v1.router import api_router
from .core.exceptions import setup_exception_handlers
from .core.middleware import setup_middleware

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    
    # Validate configuration
    try:
        validate_configuration()
        settings = get_settings()
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        raise
    
    # Create FastAPI app
    app = FastAPI(
        title=settings.app_name,
        description="Cyber Threat Intelligence Dashboard - Comprehensive threat analysis and monitoring",
        version=settings.app_version,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None
    )
    
    # Setup middleware
    setup_middleware(app)
    
    # Setup exception handlers
    setup_exception_handlers(app)
    
    # Include API router
    app.include_router(api_router, prefix=settings.api_prefix)
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with API information"""
        return {
            "message": f"{settings.app_name} API",
            "version": settings.app_version,
            "environment": settings.environment,
            "endpoints": {
                "health": "/health",
                "api": settings.api_prefix,
                "docs": "/docs" if settings.debug else "disabled"
            }
        }
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy",
            "environment": settings.environment,
            "version": settings.app_version
        }
    
    # Startup event
    @app.on_event("startup")
    async def startup_event():
        """Initialize application on startup"""
        logger.info(f"🛡️ {settings.app_name} starting up...")
        logger.info(f"Environment: {settings.environment}")
        logger.info(f"Debug mode: {settings.debug}")
        logger.info("Core components initialized")
        logger.info(f"🚀 {settings.app_name} ready")
    
    # Shutdown event
    @app.on_event("shutdown")
    async def shutdown_event():
        """Cleanup on application shutdown"""
        logger.info(f"👋 {settings.app_name} shutting down...")
        logger.info("Cleanup completed")
    
    return app


# Create app instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    
    print(f"🛡️ Starting {settings.app_name}...")
    print(f"📚 API Documentation: http://{settings.api_host}:{settings.api_port}/docs")
    print(f"🔍 Health Check: http://{settings.api_host}:{settings.api_port}/health")
    print("⚠️  Press Ctrl+C to stop")
    
    try:
        uvicorn.run(
            "app.main:app",
            host=settings.api_host,
            port=settings.api_port,
            log_level=settings.log_level.lower(),
            reload=settings.debug
        )
    except KeyboardInterrupt:
        print(f"\n👋 {settings.app_name} stopped")
