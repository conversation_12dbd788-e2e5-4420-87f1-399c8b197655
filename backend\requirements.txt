# CTI Dashboard Backend Dependencies

# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# HTTP client for API calls
aiohttp==3.9.1
httpx==0.25.2

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# Database and ORM (for future database integration)
sqlalchemy==2.0.23
alembic==1.13.1
asyncpg==0.29.0  # PostgreSQL async driver
redis==5.0.1

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Background tasks
celery==5.3.4

# Data processing and analysis
pandas==2.1.4
numpy==1.25.2

# Logging and monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Configuration management
python-dotenv==1.0.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2  # For testing FastAPI

# Development tools
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Optional: Vector database for RAG
# faiss-cpu==1.7.4  # Uncomment if using FAISS
# sentence-transformers==2.2.2  # For embeddings