/* CTI Dashboard - Modern Design System */

:root {
    /* === DESIGN TOKENS === */

    /* Color Palette - Minimalist & Professional */
    --color-primary-50: #f0f9ff;
    --color-primary-100: #e0f2fe;
    --color-primary-200: #bae6fd;
    --color-primary-300: #7dd3fc;
    --color-primary-400: #38bdf8;
    --color-primary-500: #0ea5e9;
    --color-primary-600: #0284c7;
    --color-primary-700: #0369a1;
    --color-primary-800: #075985;
    --color-primary-900: #0c4a6e;

    --color-neutral-50: #fafafa;
    --color-neutral-100: #f5f5f5;
    --color-neutral-200: #e5e5e5;
    --color-neutral-300: #d4d4d4;
    --color-neutral-400: #a3a3a3;
    --color-neutral-500: #737373;
    --color-neutral-600: #525252;
    --color-neutral-700: #404040;
    --color-neutral-800: #262626;
    --color-neutral-900: #171717;

    --color-success-50: #f0fdf4;
    --color-success-500: #22c55e;
    --color-success-600: #16a34a;
    --color-success-700: #15803d;

    --color-warning-50: #fffbeb;
    --color-warning-500: #f59e0b;
    --color-warning-600: #d97706;
    --color-warning-700: #b45309;

    --color-danger-50: #fef2f2;
    --color-danger-500: #ef4444;
    --color-danger-600: #dc2626;
    --color-danger-700: #b91c1c;

    /* Semantic Colors */
    --color-background: var(--color-neutral-50);
    --color-surface: #ffffff;
    --color-surface-secondary: var(--color-neutral-100);
    --color-border: var(--color-neutral-200);
    --color-border-subtle: var(--color-neutral-100);
    --color-text-primary: var(--color-neutral-900);
    --color-text-secondary: var(--color-neutral-600);
    --color-text-muted: var(--color-neutral-500);

    /* Typography Scale */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */

    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Spacing Scale */
    --space-0: 0;
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */
    --space-24: 6rem;     /* 96px */

    /* Border Radius */
    --radius-none: 0;
    --radius-sm: 0.125rem;   /* 2px */
    --radius-base: 0.25rem;  /* 4px */
    --radius-md: 0.375rem;   /* 6px */
    --radius-lg: 0.5rem;     /* 8px */
    --radius-xl: 0.75rem;    /* 12px */
    --radius-2xl: 1rem;      /* 16px */
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-base: 200ms ease;
    --transition-slow: 300ms ease;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;

    /* Legacy Support (for existing components) */
    --primary-color: var(--color-primary-600);
    --secondary-color: var(--color-neutral-500);
    --success-color: var(--color-success-600);
    --danger-color: var(--color-danger-600);
    --warning-color: var(--color-warning-600);
    --info-color: var(--color-primary-500);
    --dark-color: var(--color-neutral-800);
    --light-color: var(--color-neutral-100);
    --sidebar-width: 280px;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Simplified Threat Actor Search */
.threat-actor-search {
    background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-surface) 100%);
    border: 1px solid var(--color-border-subtle);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-slow);
}

.threat-actor-search:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--color-primary-200);
}

.threat-actor-search .form-control-lg {
    border: 2px solid var(--color-border);
    border-radius: var(--radius-md);
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
    transition: var(--transition-slow);
    background: var(--color-surface);
}

.threat-actor-search .form-control-lg:focus {
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 0.2rem rgba(14, 165, 233, 0.15);
    background: var(--color-surface);
}

.threat-actor-search .form-control-lg::placeholder {
    color: var(--color-text-secondary);
    font-style: italic;
}

.threat-actor-search .btn-lg {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: var(--radius-md);
    transition: var(--transition-slow);
}

.threat-actor-search .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.search-shortcuts {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    margin-top: 0.5rem;
}

.search-shortcuts kbd {
    background: var(--color-neutral-100);
    border: 1px solid var(--color-border);
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 0.75rem;
    color: var(--color-text-primary);
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    margin-right: 0.5rem;
    color: var(--success-color);
}

.navbar-nav .nav-link {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-nav .nav-link.active {
    background-color: var(--primary-color);
}

.navbar-nav .nav-link i {
    margin-right: 0.5rem;
}

#connection-status {
    padding: 0.5rem 1rem;
}

#connection-status i {
    margin-right: 0.5rem;
}

/* Main Content */
.main-content {
    margin-top: 80px;
    padding: 2rem 1rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Stats Cards */
.stat-card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.75rem 0.75rem 0 0 !important;
    padding: 1rem 1.25rem;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
    color: var(--dark-color);
}

.card-header i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

/* Forms */
.form-control {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn {
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn i {
    margin-right: 0.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
    transform: translateY(-1px);
}

/* Tables */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
}

.alert-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #664d03;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    color: #055160;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* API Configuration Styles */
.api-config-section {
    background-color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.api-config-section h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--light-color);
}

.api-status {
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
    margin-left: 0.5rem;
}

.api-status.connected {
    color: var(--success-color);
}

.api-status.disconnected {
    color: var(--danger-color);
}

.api-status i {
    margin-right: 0.25rem;
}

/* Results Display */
.result-item {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-color);
}

.result-item h6 {
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.result-item .result-meta {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.result-item .result-content {
    color: var(--dark-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem 0.5rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}



/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.border-start-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-start-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-start-warning {
    border-left: 4px solid var(--warning-color) !important;
}

.border-start-danger {
    border-left: 4px solid var(--danger-color) !important;
}

/* Modal Enhancements */
.modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.75rem 0.75rem 0 0;
    background-color: var(--light-color);
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 0.75rem 0.75rem;
}

/* Enhanced Tables */
.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

.table-responsive {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Progress Bars */
.progress {
    height: 0.75rem;
    border-radius: 0.375rem;
    background-color: var(--light-color);
}

.progress-bar {
    border-radius: 0.375rem;
    transition: width 0.6s ease;
}

/* Enhanced Alerts */
.alert {
    border-left: 4px solid;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.alert-success {
    border-left-color: var(--success-color);
}

.alert-warning {
    border-left-color: var(--warning-color);
}

.alert-danger {
    border-left-color: var(--danger-color);
}

.alert-info {
    border-left-color: var(--info-color);
}

/* Tooltip Enhancements */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
}

/* Loading Overlays */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: inherit;
}

.loading-overlay .spinner-border {
    width: 2rem;
    height: 2rem;
}

/* Enhanced Dropdowns */
.dropdown-menu {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-indicator.online {
    background-color: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
}

.status-indicator.offline {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.status-indicator.pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: #664d03;
}

.status-indicator::before {
    content: '';
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    margin-right: 0.5rem;
    background-color: currentColor;
}

/* Interactive Elements */
.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.clickable:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Code Blocks */
.code-block {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
}

/* Timeline Components */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--light-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -2.25rem;
    top: 0.5rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 2px solid white;
    box-shadow: 0 0 0 2px var(--primary-color);
}

/* Search Components */
.search-container {
    position: relative;
}

.search-container .form-control {
    padding-left: 2.5rem;
}

.search-container .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    z-index: 5;
}

/* Metric Cards */
.metric-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: 0.75rem;
    background: linear-gradient(135deg, var(--primary-color), #0b5ed7);
    color: white;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: rotate(45deg);
}

.metric-card .metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-card .metric-label {
    font-size: 0.875rem;
    opacity: 0.9;
}
