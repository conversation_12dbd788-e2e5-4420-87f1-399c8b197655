"""
Threat Actor API endpoints
"""

import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel

from ....core.security import get_current_active_user, RequireScopes
from ....core.exceptions import ExternalServiceError, ValidationError
from ....services.actor_service import ActorService, get_actor_service

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


# Pydantic models for requests
class ThreatActorRequest(BaseModel):
    """Threat actor analysis request"""
    name: str
    aliases: List[str] = []
    description: str
    origin_country: Optional[str] = None
    motivation: List[str] = []
    target_industries: List[str] = []
    target_regions: List[str] = []
    ttps: List[str] = []
    associated_malware: List[str] = []


class ThreatActorSearchRequest(BaseModel):
    """Threat actor search request"""
    query: Optional[str] = None
    actor_types: Optional[List[str]] = None
    motivations: Optional[List[str]] = None
    origin_countries: Optional[List[str]] = None
    target_countries: Optional[List[str]] = None
    target_industries: Optional[List[str]] = None
    severity_levels: Optional[List[str]] = None
    limit: int = 50
    offset: int = 0


# Threat actor analysis endpoints
@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_threat_actor(
    request: ThreatActorRequest,
    current_user=Depends(RequireScopes("actor:analyze")),
    actor_service: ActorService = Depends(get_actor_service)
):
    """Analyze threat actor and generate comprehensive summary"""
    try:
        # Create threat actor object
        actor_data = {
            "name": request.name,
            "aliases": request.aliases,
            "description": request.description,
            "origin_country": request.origin_country,
            "motivation": request.motivation,
            "target_industries": request.target_industries,
            "target_regions": request.target_regions,
            "ttps": request.ttps,
            "associated_malware": request.associated_malware,
            "analyzed_by": current_user.username
        }

        # Generate analysis
        analysis = await actor_service.analyze_threat_actor(actor_data)

        return {
            "success": True,
            "actor": {
                "name": request.name,
                "aliases": request.aliases
            },
            "analysis": {
                "executive_summary": analysis.executive_summary,
                "attack_vectors": analysis.attack_vectors,
                "target_analysis": analysis.target_analysis,
                "mitre_techniques": analysis.mitre_techniques,
                "risk_assessment": analysis.risk_assessment,
                "recommendations": analysis.recommendations,
                "confidence_level": analysis.confidence_level,
                "analysis_timestamp": analysis.created_at.isoformat()
            }
        }

    except ValidationError as e:
        logger.error(f"Actor validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except ExternalServiceError as e:
        logger.error(f"External service error during actor analysis: {e}")
        raise HTTPException(status_code=502, detail=f"External service error: {e.message}")
    except Exception as e:
        logger.error(f"Actor analysis error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during actor analysis")


@router.post("/report", response_model=Dict[str, Any])
async def generate_actor_report(
    request: ThreatActorRequest,
    format: str = Query("json", description="Report format: json, pdf, html"),
    current_user=Depends(RequireScopes("actor:report")),
    actor_service: ActorService = Depends(get_actor_service)
):
    """Generate formatted threat actor report"""
    try:
        # Create threat actor object
        actor_data = {
            "name": request.name,
            "aliases": request.aliases,
            "description": request.description,
            "origin_country": request.origin_country,
            "motivation": request.motivation,
            "target_industries": request.target_industries,
            "target_regions": request.target_regions,
            "ttps": request.ttps,
            "associated_malware": request.associated_malware,
            "analyzed_by": current_user.username
        }

        # Generate analysis and report
        report = await actor_service.generate_actor_report(actor_data, format)

        return {
            "success": True,
            "report": report,
            "format": format,
            "generated_at": report.get("generated_at")
        }

    except ValidationError as e:
        logger.error(f"Actor report validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Actor report generation error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during report generation")


# Cyfirma integration endpoints
@router.get("/cyfirma/search", response_model=Dict[str, Any])
async def search_cyfirma_threat_actors(
    query: Optional[str] = Query(None, description="Search query"),
    actor_types: Optional[str] = Query(None, description="Comma-separated actor types"),
    motivations: Optional[str] = Query(None, description="Comma-separated motivations"),
    origin_countries: Optional[str] = Query(None, description="Comma-separated origin countries"),
    target_countries: Optional[str] = Query(None, description="Comma-separated target countries"),
    target_industries: Optional[str] = Query(None, description="Comma-separated target industries"),
    severity_levels: Optional[str] = Query(None, description="Comma-separated severity levels"),
    limit: int = Query(50, description="Maximum number of results"),
    offset: int = Query(0, description="Offset for pagination"),
    current_user=Depends(RequireScopes("actor:read")),
    actor_service: ActorService = Depends(get_actor_service)
):
    """Search Cyfirma threat actors with advanced filtering"""
    try:
        # Parse comma-separated parameters
        def parse_csv_param(param: Optional[str]) -> Optional[List[str]]:
            return [item.strip() for item in param.split(",")] if param else None

        search_params = {
            "query": query,
            "actor_types": parse_csv_param(actor_types),
            "motivations": parse_csv_param(motivations),
            "origin_countries": parse_csv_param(origin_countries),
            "target_countries": parse_csv_param(target_countries),
            "target_industries": parse_csv_param(target_industries),
            "severity_levels": parse_csv_param(severity_levels),
            "limit": min(limit, 100),  # Cap at 100
            "offset": offset
        }

        # Search using actor service
        result = await actor_service.search_cyfirma_actors(search_params)
        
        return {
            "success": True,
            "total_count": result.total_count,
            "actors": result.actors,
            "facets": result.facets,
            "query_time_ms": result.query_time_ms,
            "search_params": search_params
        }

    except ExternalServiceError as e:
        logger.error(f"Cyfirma search error: {e}")
        return {
            "success": False,
            "total_count": 0,
            "actors": [],
            "facets": {},
            "query_time_ms": 0,
            "error": str(e)
        }
    except Exception as e:
        logger.error(f"Actor search error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during actor search")


@router.get("/cyfirma/search/simple", response_model=Dict[str, Any])
async def search_cyfirma_by_name(
    name: str = Query(..., description="Threat actor name to search"),
    limit: int = Query(50, description="Maximum number of results"),
    offset: int = Query(0, description="Offset for pagination"),
    current_user=Depends(RequireScopes("actor:read")),
    actor_service: ActorService = Depends(get_actor_service)
):
    """Simplified search for threat actors by name only"""
    try:
        # Create simplified search request focusing only on name
        search_params = {
            "query": name.strip(),
            "limit": min(limit, 100),  # Cap at 100
            "offset": offset
        }

        # Search using actor service
        result = await actor_service.search_cyfirma_actors(search_params)

        return {
            "success": True,
            "total_count": result.total_count,
            "actors": result.actors,
            "facets": result.facets,
            "query_time_ms": result.query_time_ms,
            "search_query": name,
            "search_type": "name_only"
        }

    except ExternalServiceError as e:
        logger.error(f"Simplified Cyfirma search error: {e}")
        return {
            "success": False,
            "total_count": 0,
            "actors": [],
            "facets": {},
            "query_time_ms": 0,
            "search_query": name,
            "search_type": "name_only",
            "error": str(e)
        }
    except Exception as e:
        logger.error(f"Simplified actor search error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during simplified actor search")


@router.get("/cyfirma/{actor_name}", response_model=Dict[str, Any])
async def get_cyfirma_threat_actor(
    actor_name: str,
    current_user=Depends(RequireScopes("actor:read")),
    actor_service: ActorService = Depends(get_actor_service)
):
    """Get specific threat actor details from Cyfirma"""
    try:
        actor_data = await actor_service.get_cyfirma_threat_actor(actor_name)
        
        if not actor_data:
            raise HTTPException(status_code=404, detail=f"Threat actor '{actor_name}' not found")

        return {
            "success": True,
            "actor": actor_data.processed_actor,
            "raw_stix": actor_data.raw_stix,
            "retrieved_at": actor_data.retrieved_at.isoformat()
        }

    except HTTPException:
        raise
    except ExternalServiceError as e:
        logger.error(f"Cyfirma API error: {e}")
        raise HTTPException(status_code=502, detail=f"Cyfirma API error: {e.message}")
    except Exception as e:
        logger.error(f"Failed to get Cyfirma threat actor: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/cyfirma/stats", response_model=Dict[str, Any])
async def get_cyfirma_stats(
    current_user=Depends(RequireScopes("actor:read")),
    actor_service: ActorService = Depends(get_actor_service)
):
    """Get statistics about Cyfirma threat actor data"""
    try:
        stats = await actor_service.get_cyfirma_statistics()

        return {
            "success": True,
            "stats": stats,
            "generated_at": stats.get("generated_at")
        }

    except ExternalServiceError as e:
        logger.error(f"Cyfirma stats error: {e}")
        raise HTTPException(status_code=502, detail=f"Cyfirma API error: {e.message}")
    except Exception as e:
        logger.error(f"Failed to get Cyfirma stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Local threat actor management
@router.get("/", response_model=Dict[str, Any])
async def list_threat_actors(
    query: Optional[str] = Query(None, description="Search query"),
    limit: int = Query(50, description="Maximum number of results"),
    offset: int = Query(0, description="Offset for pagination"),
    current_user=Depends(RequireScopes("actor:read")),
    actor_service: ActorService = Depends(get_actor_service)
):
    """List local threat actors"""
    try:
        actors = await actor_service.list_local_actors(
            query=query,
            limit=min(limit, 100),
            offset=offset
        )

        return {
            "success": True,
            "total": actors.total,
            "actors": [
                {
                    "id": actor.id,
                    "name": actor.name,
                    "aliases": actor.aliases,
                    "origin_country": actor.origin_country,
                    "primary_motivation": actor.primary_motivation,
                    "severity_level": actor.severity_level,
                    "created_at": actor.created_at.isoformat(),
                    "updated_at": actor.updated_at.isoformat()
                } for actor in actors.items
            ],
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        logger.error(f"List actors error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
