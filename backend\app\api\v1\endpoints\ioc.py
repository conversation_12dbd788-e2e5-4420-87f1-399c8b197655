"""
IoC (Indicators of Compromise) API endpoints
"""

import logging
from typing import Dict, List, Any
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from ....core.security import get_current_active_user, RequireScopes
from ....core.exceptions import ExternalServiceError, ValidationError
from ....services.ioc_service import IoCService, get_ioc_service

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Pydantic models for requests
class IoCRequest(BaseModel):
    """Single IoC ingestion request"""
    value: str
    source: str
    threat_actor: str = None
    malware_family: str = None
    tags: List[str] = []


class IoCBatchRequest(BaseModel):
    """Batch IoC ingestion request"""
    iocs: List[IoCRequest]


class IoCSearchRequest(BaseModel):
    """IoC search request"""
    query: str = None
    ioc_type: str = None
    threat_actor: str = None
    malware_family: str = None
    tags: List[str] = []
    confidence_min: float = None
    limit: int = 50
    offset: int = 0


# IoC ingestion endpoints
@router.post("/ingest", response_model=Dict[str, Any])
async def ingest_ioc(
    request: IoCRequest,
    current_user=Depends(RequireScopes("ioc:write")),
    ioc_service: IoCService = Depends(get_ioc_service)
):
    """Ingest and enrich a single IoC"""
    try:
        enriched_ioc = await ioc_service.ingest_ioc(
            value=request.value,
            source=request.source,
            threat_actor=request.threat_actor,
            malware_family=request.malware_family,
            tags=request.tags,
            created_by=current_user.username
        )

        return {
            "success": True,
            "ioc": {
                "id": enriched_ioc.id,
                "value": enriched_ioc.value,
                "type": enriched_ioc.ioc_type,
                "confidence": enriched_ioc.confidence,
                "threat_actor": enriched_ioc.threat_actor,
                "malware_family": enriched_ioc.malware_family,
                "tags": enriched_ioc.tags,
                "enrichment_sources": list(enriched_ioc.enrichment_data.keys()) if enriched_ioc.enrichment_data else [],
                "created_at": enriched_ioc.created_at.isoformat(),
                "created_by": enriched_ioc.created_by
            }
        }

    except ValidationError as e:
        logger.error(f"IoC validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except ExternalServiceError as e:
        logger.error(f"External service error during IoC ingestion: {e}")
        raise HTTPException(status_code=502, detail=f"External service error: {e.message}")
    except Exception as e:
        logger.error(f"IoC ingestion error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during IoC ingestion")


@router.post("/batch", response_model=Dict[str, Any])
async def batch_ingest_iocs(
    request: IoCBatchRequest,
    current_user=Depends(RequireScopes("ioc:write")),
    ioc_service: IoCService = Depends(get_ioc_service)
):
    """Batch ingest multiple IoCs"""
    try:
        if len(request.iocs) > 100:  # Limit batch size
            raise HTTPException(status_code=400, detail="Batch size cannot exceed 100 IoCs")

        ioc_data = []
        for ioc_req in request.iocs:
            ioc_data.append({
                'value': ioc_req.value,
                'source': ioc_req.source,
                'threat_actor': ioc_req.threat_actor,
                'malware_family': ioc_req.malware_family,
                'tags': ioc_req.tags,
                'created_by': current_user.username
            })

        enriched_iocs = await ioc_service.batch_ingest(ioc_data)

        return {
            "success": True,
            "processed": len(enriched_iocs),
            "total_requested": len(request.iocs),
            "iocs": [
                {
                    "id": ioc.id,
                    "value": ioc.value,
                    "type": ioc.ioc_type,
                    "confidence": ioc.confidence,
                    "created_at": ioc.created_at.isoformat()
                } for ioc in enriched_iocs
            ]
        }

    except ValidationError as e:
        logger.error(f"Batch IoC validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Batch IoC ingestion error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during batch IoC ingestion")


# IoC retrieval endpoints
@router.get("/search", response_model=Dict[str, Any])
async def search_iocs(
    query: str = None,
    ioc_type: str = None,
    threat_actor: str = None,
    malware_family: str = None,
    confidence_min: float = None,
    limit: int = 50,
    offset: int = 0,
    current_user=Depends(RequireScopes("ioc:read")),
    ioc_service: IoCService = Depends(get_ioc_service)
):
    """Search IoCs with filters"""
    try:
        search_params = {
            "query": query,
            "ioc_type": ioc_type,
            "threat_actor": threat_actor,
            "malware_family": malware_family,
            "confidence_min": confidence_min,
            "limit": min(limit, 100),  # Cap at 100
            "offset": offset
        }

        results = await ioc_service.search_iocs(**search_params)

        return {
            "success": True,
            "total": results.total,
            "limit": limit,
            "offset": offset,
            "iocs": [
                {
                    "id": ioc.id,
                    "value": ioc.value,
                    "type": ioc.ioc_type,
                    "confidence": ioc.confidence,
                    "threat_actor": ioc.threat_actor,
                    "malware_family": ioc.malware_family,
                    "tags": ioc.tags,
                    "first_seen": ioc.first_seen.isoformat() if ioc.first_seen else None,
                    "last_seen": ioc.last_seen.isoformat() if ioc.last_seen else None,
                    "created_at": ioc.created_at.isoformat()
                } for ioc in results.items
            ]
        }

    except Exception as e:
        logger.error(f"IoC search error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during IoC search")


@router.get("/{ioc_id}", response_model=Dict[str, Any])
async def get_ioc(
    ioc_id: int,
    current_user=Depends(RequireScopes("ioc:read")),
    ioc_service: IoCService = Depends(get_ioc_service)
):
    """Get specific IoC by ID"""
    try:
        ioc = await ioc_service.get_ioc_by_id(ioc_id)
        
        if not ioc:
            raise HTTPException(status_code=404, detail="IoC not found")

        return {
            "success": True,
            "ioc": {
                "id": ioc.id,
                "value": ioc.value,
                "type": ioc.ioc_type,
                "confidence": ioc.confidence,
                "threat_actor": ioc.threat_actor,
                "malware_family": ioc.malware_family,
                "tags": ioc.tags,
                "enrichment_data": ioc.enrichment_data,
                "first_seen": ioc.first_seen.isoformat() if ioc.first_seen else None,
                "last_seen": ioc.last_seen.isoformat() if ioc.last_seen else None,
                "created_at": ioc.created_at.isoformat(),
                "updated_at": ioc.updated_at.isoformat(),
                "created_by": ioc.created_by
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get IoC error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{ioc_id}")
async def delete_ioc(
    ioc_id: int,
    current_user=Depends(RequireScopes("ioc:delete")),
    ioc_service: IoCService = Depends(get_ioc_service)
):
    """Delete an IoC"""
    try:
        success = await ioc_service.delete_ioc(ioc_id, current_user.username)
        
        if not success:
            raise HTTPException(status_code=404, detail="IoC not found")

        return {
            "success": True,
            "message": "IoC deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete IoC error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{ioc_id}/enrichment", response_model=Dict[str, Any])
async def get_ioc_enrichment(
    ioc_id: int,
    refresh: bool = False,
    current_user=Depends(RequireScopes("ioc:read")),
    ioc_service: IoCService = Depends(get_ioc_service)
):
    """Get or refresh IoC enrichment data"""
    try:
        if refresh:
            enrichment_data = await ioc_service.refresh_ioc_enrichment(ioc_id)
        else:
            ioc = await ioc_service.get_ioc_by_id(ioc_id)
            if not ioc:
                raise HTTPException(status_code=404, detail="IoC not found")
            enrichment_data = ioc.enrichment_data

        return {
            "success": True,
            "enrichment_data": enrichment_data,
            "refreshed": refresh
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get IoC enrichment error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
