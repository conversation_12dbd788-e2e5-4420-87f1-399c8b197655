#!/usr/bin/env python3
"""
Test runner script for CTI Dashboard
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, cwd=None):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return e.returncode, e.stdout, e.stderr


def setup_test_environment():
    """Set up test environment"""
    print("🔧 Setting up test environment...")
    
    # Set test environment variables
    os.environ["ENVIRONMENT"] = "testing"
    os.environ["DEBUG"] = "true"
    os.environ["DATABASE_URL"] = "sqlite:///./test.db"
    os.environ["SECRET_KEY"] = "test_secret_key_for_testing_only_32_chars"
    
    print("✅ Test environment configured")


def install_test_dependencies():
    """Install test dependencies"""
    print("📦 Installing test dependencies...")
    
    test_requirements = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-mock>=3.10.0",
        "pytest-timeout>=2.1.0",
        "httpx>=0.24.0",
        "factory-boy>=3.2.0"
    ]
    
    for requirement in test_requirements:
        returncode, stdout, stderr = run_command(f"pip install {requirement}")
        if returncode != 0:
            print(f"❌ Failed to install {requirement}")
            print(f"Error: {stderr}")
            return False
    
    print("✅ Test dependencies installed")
    return True


def run_unit_tests(verbose=False, coverage=False):
    """Run unit tests"""
    print("🧪 Running unit tests...")
    
    cmd = "pytest app/tests/test_*.py -m 'not integration and not external'"
    
    if verbose:
        cmd += " -v"
    
    if coverage:
        cmd += " --cov=app --cov-report=term-missing"
    
    returncode, stdout, stderr = run_command(cmd)
    
    print(stdout)
    if stderr:
        print(stderr)
    
    if returncode == 0:
        print("✅ Unit tests passed")
    else:
        print("❌ Unit tests failed")
    
    return returncode == 0


def run_integration_tests(verbose=False):
    """Run integration tests"""
    print("🔗 Running integration tests...")
    
    cmd = "pytest app/tests/test_integration.py -m integration"
    
    if verbose:
        cmd += " -v"
    
    returncode, stdout, stderr = run_command(cmd)
    
    print(stdout)
    if stderr:
        print(stderr)
    
    if returncode == 0:
        print("✅ Integration tests passed")
    else:
        print("❌ Integration tests failed")
    
    return returncode == 0


def run_security_tests(verbose=False):
    """Run security tests"""
    print("🔒 Running security tests...")
    
    cmd = "pytest app/tests/test_security.py -m security"
    
    if verbose:
        cmd += " -v"
    
    returncode, stdout, stderr = run_command(cmd)
    
    print(stdout)
    if stderr:
        print(stderr)
    
    if returncode == 0:
        print("✅ Security tests passed")
    else:
        print("❌ Security tests failed")
    
    return returncode == 0


def run_performance_tests(verbose=False):
    """Run performance tests"""
    print("⚡ Running performance tests...")
    
    cmd = "pytest app/tests/ -m performance"
    
    if verbose:
        cmd += " -v"
    
    returncode, stdout, stderr = run_command(cmd)
    
    print(stdout)
    if stderr:
        print(stderr)
    
    if returncode == 0:
        print("✅ Performance tests passed")
    else:
        print("❌ Performance tests failed")
    
    return returncode == 0


def run_all_tests(verbose=False, coverage=False):
    """Run all tests"""
    print("🚀 Running all tests...")
    
    cmd = "pytest app/tests/ -m 'not external'"
    
    if verbose:
        cmd += " -v"
    
    if coverage:
        cmd += " --cov=app --cov-report=term-missing --cov-report=html"
    
    returncode, stdout, stderr = run_command(cmd)
    
    print(stdout)
    if stderr:
        print(stderr)
    
    if returncode == 0:
        print("✅ All tests passed")
    else:
        print("❌ Some tests failed")
    
    return returncode == 0


def run_linting():
    """Run code linting"""
    print("🔍 Running code linting...")
    
    # Check if flake8 is installed
    returncode, _, _ = run_command("flake8 --version")
    if returncode != 0:
        print("Installing flake8...")
        run_command("pip install flake8")
    
    # Run flake8
    cmd = "flake8 app/ --max-line-length=120 --exclude=__pycache__,migrations"
    returncode, stdout, stderr = run_command(cmd)
    
    if stdout:
        print(stdout)
    if stderr:
        print(stderr)
    
    if returncode == 0:
        print("✅ Linting passed")
    else:
        print("❌ Linting failed")
    
    return returncode == 0


def run_type_checking():
    """Run type checking with mypy"""
    print("🔍 Running type checking...")
    
    # Check if mypy is installed
    returncode, _, _ = run_command("mypy --version")
    if returncode != 0:
        print("Installing mypy...")
        run_command("pip install mypy")
    
    # Run mypy
    cmd = "mypy app/ --ignore-missing-imports"
    returncode, stdout, stderr = run_command(cmd)
    
    if stdout:
        print(stdout)
    if stderr:
        print(stderr)
    
    if returncode == 0:
        print("✅ Type checking passed")
    else:
        print("❌ Type checking failed")
    
    return returncode == 0


def generate_coverage_report():
    """Generate coverage report"""
    print("📊 Generating coverage report...")
    
    cmd = "pytest app/tests/ --cov=app --cov-report=html --cov-report=xml --cov-report=term"
    returncode, stdout, stderr = run_command(cmd)
    
    print(stdout)
    if stderr:
        print(stderr)
    
    if returncode == 0:
        print("✅ Coverage report generated")
        print("📁 HTML report: htmlcov/index.html")
        print("📁 XML report: coverage.xml")
    else:
        print("❌ Coverage report generation failed")
    
    return returncode == 0


def clean_test_artifacts():
    """Clean test artifacts"""
    print("🧹 Cleaning test artifacts...")
    
    artifacts = [
        "test.db",
        ".coverage",
        "htmlcov/",
        ".pytest_cache/",
        "__pycache__/",
        "*.pyc",
        "coverage.xml"
    ]
    
    for artifact in artifacts:
        run_command(f"rm -rf {artifact}")
    
    print("✅ Test artifacts cleaned")


def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description="CTI Dashboard Test Runner")
    parser.add_argument("--type", choices=["unit", "integration", "security", "performance", "all"], 
                       default="all", help="Type of tests to run")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--coverage", "-c", action="store_true", help="Generate coverage report")
    parser.add_argument("--lint", action="store_true", help="Run linting")
    parser.add_argument("--type-check", action="store_true", help="Run type checking")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--clean", action="store_true", help="Clean test artifacts")
    
    args = parser.parse_args()
    
    # Change to backend directory
    backend_dir = Path(__file__).parent.parent
    os.chdir(backend_dir)
    
    print("🛡️ CTI Dashboard Test Runner")
    print("=" * 50)
    
    success = True
    
    # Setup test environment
    setup_test_environment()
    
    # Install dependencies if requested
    if args.install_deps:
        if not install_test_dependencies():
            return 1
    
    # Clean artifacts if requested
    if args.clean:
        clean_test_artifacts()
        return 0
    
    # Run linting if requested
    if args.lint:
        if not run_linting():
            success = False
    
    # Run type checking if requested
    if args.type_check:
        if not run_type_checking():
            success = False
    
    # Run tests based on type
    if args.type == "unit":
        if not run_unit_tests(args.verbose, args.coverage):
            success = False
    elif args.type == "integration":
        if not run_integration_tests(args.verbose):
            success = False
    elif args.type == "security":
        if not run_security_tests(args.verbose):
            success = False
    elif args.type == "performance":
        if not run_performance_tests(args.verbose):
            success = False
    elif args.type == "all":
        if not run_all_tests(args.verbose, args.coverage):
            success = False
    
    # Generate coverage report if requested
    if args.coverage and args.type in ["unit", "all"]:
        generate_coverage_report()
    
    print("=" * 50)
    if success:
        print("🎉 All tests completed successfully!")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
