@echo off
echo 🛡️ CTI Dashboard Frontend Server
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.6+ and try again
    pause
    exit /b 1
)

REM Check if frontend directory exists
if not exist "frontend" (
    echo ❌ Frontend directory not found!
    echo Make sure you're running this from the project root
    pause
    exit /b 1
)

echo 📁 Starting frontend server...
echo 🌐 Server will be available at: http://localhost:8080
echo 📚 Main dashboard: http://localhost:8080/index.html
echo 🎨 Component demo: http://localhost:8080/demo.html
echo.
echo ⚠️  Make sure your backend is running on http://127.0.0.1:8000
echo 🔧 Press Ctrl+C to stop the server
echo.

REM Start the Python server
python start_frontend.py

pause
