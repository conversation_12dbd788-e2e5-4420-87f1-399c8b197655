#!/usr/bin/env python3
"""
Simple HTTP server to serve the CTI Dashboard frontend
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def main():
    # Configuration
    PORT = 8080
    FRONTEND_DIR = Path(__file__).parent / "frontend"
    
    print("🛡️ CTI Dashboard Frontend Server")
    print("=" * 40)
    
    # Check if frontend directory exists
    if not FRONTEND_DIR.exists():
        print("❌ Frontend directory not found!")
        print(f"Expected: {FRONTEND_DIR}")
        sys.exit(1)
    
    # Change to frontend directory
    os.chdir(FRONTEND_DIR)
    
    # Create server
    Handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print(f"📁 Serving files from: {FRONTEND_DIR}")
            print(f"🌐 Server running at: http://localhost:{PORT}")
            print(f"📚 Main dashboard: http://localhost:{PORT}/index.html")
            print(f"🎨 Component demo: http://localhost:{PORT}/demo.html")
            print()
            print("⚠️  Make sure your backend is running on http://127.0.0.1:8000")
            print("🔧 Press Ctrl+C to stop the server")
            print()
            
            # Try to open browser
            try:
                webbrowser.open(f"http://localhost:{PORT}/index.html")
                print("🚀 Opening browser...")
            except Exception as e:
                print(f"⚠️  Could not open browser automatically: {e}")
                print("Please manually open http://localhost:8080/index.html")
            
            print()
            print("Server started successfully!")
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {PORT} is already in use!")
            print("Try stopping other servers or use a different port")
        else:
            print(f"❌ Server error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
