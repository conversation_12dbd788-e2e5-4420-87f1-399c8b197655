/* Light Professional Theme - Clean & Corporate */

:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    
    /* Secondary Colors */
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: #ffffff;
    --bg-hover: #f8fafc;
    
    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #94a3b8;
    --text-accent: #2563eb;
    
    /* Border Colors */
    --border-color: #e2e8f0;
    --border-accent: #2563eb;
    
    /* Shadow Colors */
    --shadow-primary: rgba(37, 99, 235, 0.1);
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    
    /* Gradient Backgrounds */
    --gradient-primary: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    --gradient-accent: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
    --gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    --gradient-danger: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

/* Global Styles */
body {
    background: var(--gradient-primary);
    color: var(--text-primary);
    font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
    min-height: 100vh;
}

/* Theme Transition Effects */
body.theme-transitioning {
    transition: background-color 0.3s ease, color 0.3s ease;
}

body.theme-transitioning * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 1px 3px var(--shadow-light);
}

.navbar-brand {
    color: var(--text-primary) !important;
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-brand i {
    color: var(--primary-color);
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background: rgba(37, 99, 235, 0.05);
}

.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    background: rgba(37, 99, 235, 0.1);
    font-weight: 600;
}

/* Cards */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 1px 3px var(--shadow-light);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-medium);
    border-color: var(--border-accent);
}

.card-header {
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    font-weight: 600;
    border-radius: 12px 12px 0 0;
}

.card-header h5 {
    color: var(--text-accent);
    margin: 0;
}

/* Stat Cards */
.stat-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px var(--shadow-medium);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    box-shadow: 0 4px 12px var(--shadow-medium);
}

.stat-icon.bg-primary {
    background: var(--gradient-accent);
}

.stat-icon.bg-success {
    background: var(--gradient-success);
}

.stat-icon.bg-warning {
    background: var(--gradient-warning);
}

.stat-icon.bg-info {
    background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
}

/* Buttons */
.btn {
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: none;
    text-transform: none;
    letter-spacing: 0.025em;
}

.btn-primary {
    background: var(--gradient-accent);
    box-shadow: 0 2px 4px var(--shadow-primary);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-primary);
}

.btn-success {
    background: var(--gradient-success);
    box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
}

.btn-warning {
    background: var(--gradient-warning);
    box-shadow: 0 2px 4px rgba(217, 119, 6, 0.2);
}

.btn-danger {
    background: var(--gradient-danger);
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    transform: translateY(-1px);
}

/* Forms */
.form-control {
    background: var(--bg-card);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem var(--shadow-primary);
    background: var(--bg-card);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-select {
    background: var(--bg-card);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem var(--shadow-primary);
}

/* Tables */
.table {
    color: var(--text-primary);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px var(--shadow-light);
}

.table thead th {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
    font-weight: 700;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
}

.table tbody tr {
    border-color: var(--border-color);
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: var(--bg-hover);
}

.table-responsive {
    border-radius: 12px;
    box-shadow: 0 1px 3px var(--shadow-light);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 8px;
    border-left: 4px solid;
    box-shadow: 0 1px 3px var(--shadow-light);
}

.alert-success {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
    border-left-color: var(--success-color);
}

.alert-warning {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
    border-left-color: var(--warning-color);
}

.alert-danger {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
    border-left-color: var(--danger-color);
}

.alert-info {
    background: rgba(8, 145, 178, 0.1);
    color: var(--info-color);
    border-left-color: var(--info-color);
}

/* Badges */
.badge {
    font-weight: 600;
    border-radius: 6px;
    padding: 0.375rem 0.75rem;
}

.badge.bg-primary {
    background: var(--primary-color) !important;
}

.badge.bg-success {
    background: var(--success-color) !important;
}

.badge.bg-warning {
    background: var(--warning-color) !important;
}

.badge.bg-danger {
    background: var(--danger-color) !important;
}

.badge.bg-info {
    background: var(--info-color) !important;
}

.badge.bg-secondary {
    background: var(--secondary-color) !important;
}

/* Progress Bars */
.progress {
    height: 0.75rem;
    border-radius: 6px;
    background: var(--bg-tertiary);
    box-shadow: inset 0 1px 2px var(--shadow-light);
}

.progress-bar {
    border-radius: 6px;
    transition: width 0.6s ease;
}

/* Loading States */
.spinner-border {
    border-color: var(--primary-color);
    border-right-color: transparent;
}

.loading-overlay {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
}

/* Dropdown */
.dropdown-menu {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow-medium);
    background: var(--bg-card);
}

.dropdown-item {
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

/* Theme Toggle Button */
#theme-toggle {
    border: 2px solid var(--border-color);
    background: transparent;
    color: var(--text-secondary);
    border-radius: 8px;
    padding: 0.375rem 0.75rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

#theme-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    transform: translateY(-1px);
}

#theme-toggle:focus {
    box-shadow: 0 0 0 0.2rem var(--shadow-primary);
    border-color: var(--primary-color);
}

#theme-icon {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

#theme-toggle:hover #theme-icon {
    transform: rotate(15deg);
}

/* Connection Status */
#connection-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background: rgba(5, 150, 105, 0.1);
    border: 1px solid rgba(5, 150, 105, 0.2);
    color: var(--success-color);
}

/* API Configuration */
.api-config-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 1px 3px var(--shadow-light);
}

.api-config-section h6 {
    color: var(--text-accent);
    border-bottom: 2px solid var(--bg-tertiary);
}

/* Timeline */
.timeline::before {
    background: var(--border-color);
}

.timeline-item::before {
    background: var(--primary-color);
    border-color: var(--bg-card);
    box-shadow: 0 0 0 3px var(--primary-color);
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .card {
        border-radius: 8px;
        margin-bottom: 1rem;
    }
    
    .btn {
        border-radius: 6px;
    }
    
    .form-control {
        border-radius: 6px;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
}
