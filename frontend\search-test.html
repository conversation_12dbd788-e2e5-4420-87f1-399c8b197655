<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTI Dashboard - Simplified Search Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            background: #f8f9fa;
        }
        .demo-search {
            background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
            border: 2px solid #2196f3;
            border-radius: 1rem;
            padding: 2rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container test-container">
        <div class="text-center mb-4">
            <h1><i class="bi bi-shield-check text-primary"></i> CTI Dashboard</h1>
            <h2>Simplified Threat Actor Search Test</h2>
            <p class="text-muted">Testing the streamlined search functionality</p>
        </div>

        <!-- Test Section 1: Basic Search -->
        <div class="test-section">
            <h3><i class="bi bi-search"></i> Basic Search Test</h3>
            <p>This demonstrates the simplified search interface with a single input field for threat actor names.</p>
            
            <div class="demo-search">
                <form id="test-search-form">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="test-actor-name" class="form-label fw-semibold">Threat Actor Name</label>
                                <input type="text" class="form-control form-control-lg" id="test-actor-name" 
                                       placeholder="Enter threat actor name (e.g., APT29, Lazarus Group, FIN7...)" 
                                       autocomplete="off">
                                <div class="form-text">Search by exact threat actor name or common aliases</div>
                                <div class="search-shortcuts mt-2">
                                    <small>
                                        <kbd>Enter</kbd> to search • <kbd>Esc</kbd> to clear
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <div class="mb-3 w-100">
                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="bi bi-search"></i> Search Actor
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearTestSearch()">
                                <i class="bi bi-x-circle"></i> Clear Search
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm ms-2" onclick="loadTestData()">
                                <i class="bi bi-list"></i> Load Test Data
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Test Section 2: Results Display -->
        <div class="test-section">
            <h3><i class="bi bi-database"></i> Search Results</h3>
            <div id="test-results">
                <div class="text-center text-muted">
                    <i class="bi bi-search display-4"></i>
                    <p class="mt-2">Enter a threat actor name above to test the search functionality.</p>
                </div>
            </div>
        </div>

        <!-- Test Section 3: Features -->
        <div class="test-section">
            <h3><i class="bi bi-list-check"></i> Features Tested</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>✅ Implemented Features:</h5>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check-circle text-success"></i> Single input field for actor name</li>
                        <li><i class="bi bi-check-circle text-success"></i> Keyboard shortcuts (Enter/Esc)</li>
                        <li><i class="bi bi-check-circle text-success"></i> Auto-trim whitespace</li>
                        <li><i class="bi bi-check-circle text-success"></i> Input validation</li>
                        <li><i class="bi bi-check-circle text-success"></i> Loading states</li>
                        <li><i class="bi bi-check-circle text-success"></i> Error handling</li>
                        <li><i class="bi bi-check-circle text-success"></i> Responsive design</li>
                        <li><i class="bi bi-check-circle text-success"></i> Professional styling</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>🎯 Key Benefits:</h5>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-arrow-right text-primary"></i> Simplified user experience</li>
                        <li><i class="bi bi-arrow-right text-primary"></i> Faster search operations</li>
                        <li><i class="bi bi-arrow-right text-primary"></i> Reduced cognitive load</li>
                        <li><i class="bi bi-arrow-right text-primary"></i> Better accessibility</li>
                        <li><i class="bi bi-arrow-right text-primary"></i> Mobile-friendly interface</li>
                        <li><i class="bi bi-arrow-right text-primary"></i> Consistent with CTI workflows</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test search functionality
        document.getElementById('test-search-form').addEventListener('submit', function(e) {
            e.preventDefault();
            performTestSearch();
        });

        // Keyboard shortcuts
        document.getElementById('test-actor-name').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performTestSearch();
            }
        });

        document.getElementById('test-actor-name').addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                clearTestSearch();
            }
        });

        // Auto-trim whitespace
        document.getElementById('test-actor-name').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/^\s+/, '');
        });

        function performTestSearch() {
            const actorName = document.getElementById('test-actor-name').value.trim();
            const resultsContainer = document.getElementById('test-results');
            const submitButton = document.querySelector('#test-search-form button[type="submit"]');

            if (!actorName) {
                showTestAlert('Please enter a threat actor name to search.', 'warning');
                document.getElementById('test-actor-name').focus();
                return;
            }

            // Show loading state
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Searching...';
            
            resultsContainer.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Searching for "${actorName}" in test database...</p>
                </div>
            `;

            // Simulate API call
            setTimeout(() => {
                displayTestResults(actorName);
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="bi bi-search"></i> Search Actor';
            }, 1500);
        }

        function displayTestResults(actorName) {
            const resultsContainer = document.getElementById('test-results');
            
            // Mock results for demonstration
            const mockResults = [
                { name: 'APT29', type: 'Nation State', origin: 'Russia', confidence: 0.95 },
                { name: 'Lazarus Group', type: 'Nation State', origin: 'North Korea', confidence: 0.92 },
                { name: 'FIN7', type: 'Crime Syndicate', origin: 'Unknown', confidence: 0.88 }
            ];

            const matchingResults = mockResults.filter(actor => 
                actor.name.toLowerCase().includes(actorName.toLowerCase())
            );

            if (matchingResults.length > 0) {
                let resultsHtml = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> Found ${matchingResults.length} result(s) for "${actorName}"
                    </div>
                `;

                matchingResults.forEach(actor => {
                    resultsHtml += `
                        <div class="card mb-2">
                            <div class="card-body">
                                <h6 class="card-title">${actor.name}</h6>
                                <p class="card-text">
                                    <span class="badge bg-primary">${actor.type}</span>
                                    <span class="badge bg-secondary">${actor.origin}</span>
                                    <span class="badge bg-success">Confidence: ${(actor.confidence * 100).toFixed(0)}%</span>
                                </p>
                            </div>
                        </div>
                    `;
                });

                resultsContainer.innerHTML = resultsHtml;
            } else {
                resultsContainer.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> No results found for "${actorName}". 
                        <br><small>Try searching for: APT29, Lazarus, or FIN7</small>
                    </div>
                `;
            }
        }

        function clearTestSearch() {
            document.getElementById('test-actor-name').value = '';
            document.getElementById('test-actor-name').focus();
            document.getElementById('test-results').innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-search display-4"></i>
                    <p class="mt-2">Enter a threat actor name above to test the search functionality.</p>
                </div>
            `;
        }

        function loadTestData() {
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> Test data includes: APT29, Lazarus Group, FIN7
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h6>APT29</h6>
                                <span class="badge bg-danger">Nation State</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h6>Lazarus Group</h6>
                                <span class="badge bg-danger">Nation State</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h6>FIN7</h6>
                                <span class="badge bg-warning">Crime Syndicate</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function showTestAlert(message, type = 'info') {
            const alertContainer = document.createElement('div');
            alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertContainer.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            
            alertContainer.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            
            document.body.appendChild(alertContainer);
            
            setTimeout(() => {
                if (alertContainer.parentNode) {
                    alertContainer.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
