"""
Cyfirma API Service - Integration with Cyfirma threat intelligence API
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
from pydantic import ValidationError

from ..schemas.threat_actor import (
    CyfirmaThreatActor, 
    ProcessedThreatActor, 
    CyfirmaAPIResponse,
    ThreatActorSearchRequest,
    ThreatActorSearchResponse
)

logger = logging.getLogger(__name__)


class CyfirmaService:
    """Service for interacting with Cyfirma threat intelligence API"""
    
    def __init__(self, api_key: str, base_url: str = "https://decyfir.cyfirma.com/core/api-ua"):
        self.api_key = api_key
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None
        self.cache: Dict[str, Any] = {}
        self.cache_ttl = timedelta(hours=6)  # Cache for 6 hours
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={"User-Agent": "CTI-Dashboard/1.0"}
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def fetch_all_threat_actors(self, use_cache: bool = True) -> CyfirmaAPIResponse:
        """Fetch all threat actors from Cyfirma API (STIX 2.1 format)"""
        cache_key = "all_threat_actors"
        
        # Check cache first
        if use_cache and self._is_cache_valid(cache_key):
            logger.info("Returning cached threat actor data")
            return self.cache[cache_key]["data"]
        
        try:
            url = f"{self.base_url}/threatactor/stix/v2.1"
            params = {"key": self.api_key}
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    raw_data = await response.json()
                    
                    # Parse and validate the data
                    threat_actors = []
                    for actor_data in raw_data:
                        try:
                            actor = CyfirmaThreatActor(**actor_data)
                            threat_actors.append(actor)
                        except ValidationError as e:
                            logger.warning(f"Failed to parse threat actor {actor_data.get('name', 'unknown')}: {e}")
                            continue
                    
                    api_response = CyfirmaAPIResponse(
                        success=True,
                        data=threat_actors,
                        total_count=len(threat_actors)
                    )
                    
                    # Cache the response
                    self._cache_data(cache_key, api_response)
                    
                    logger.info(f"Successfully fetched {len(threat_actors)} threat actors from Cyfirma")
                    return api_response
                    
                else:
                    error_msg = f"Cyfirma API error: {response.status} - {await response.text()}"
                    logger.error(error_msg)
                    return CyfirmaAPIResponse(success=False, error_message=error_msg)
                    
        except Exception as e:
            error_msg = f"Failed to fetch threat actors from Cyfirma: {str(e)}"
            logger.error(error_msg)
            return CyfirmaAPIResponse(success=False, error_message=error_msg)
    
    async def search_threat_actor(self, name: str) -> CyfirmaAPIResponse:
        """Search for specific threat actor by name"""
        try:
            url = f"{self.base_url}/threatactor/stix/v2.1"
            params = {"key": self.api_key, "name": name}
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    raw_data = await response.json()
                    
                    threat_actors = []
                    for actor_data in raw_data:
                        try:
                            actor = CyfirmaThreatActor(**actor_data)
                            threat_actors.append(actor)
                        except ValidationError as e:
                            logger.warning(f"Failed to parse threat actor: {e}")
                            continue
                    
                    return CyfirmaAPIResponse(
                        success=True,
                        data=threat_actors,
                        total_count=len(threat_actors)
                    )
                else:
                    error_msg = f"Cyfirma API error: {response.status}"
                    return CyfirmaAPIResponse(success=False, error_message=error_msg)
                    
        except Exception as e:
            error_msg = f"Failed to search threat actor: {str(e)}"
            logger.error(error_msg)
            return CyfirmaAPIResponse(success=False, error_message=error_msg)
    
    def process_threat_actor(self, cyfirma_actor: CyfirmaThreatActor) -> ProcessedThreatActor:
        """Convert Cyfirma STIX data to internal processed format"""
        
        # Extract extension properties
        extension_props = {}
        if cyfirma_actor.extensions:
            for ext_key, ext_value in cyfirma_actor.extensions.items():
                if hasattr(ext_value, 'properties'):
                    extension_props.update(ext_value.properties.dict(exclude_none=True))
        
        # Parse comma-separated fields
        target_countries = self._parse_comma_separated(extension_props.get("target-countries", ""))
        target_industries = self._parse_comma_separated(extension_props.get("target-industries", ""))
        target_technologies = self._parse_comma_separated(extension_props.get("target-technologies", ""))
        
        # Calculate confidence score based on data completeness
        confidence_score = self._calculate_confidence_score(cyfirma_actor, extension_props)
        
        # Determine severity level
        severity_level = self._determine_severity_level(cyfirma_actor, target_industries)
        
        return ProcessedThreatActor(
            stix_id=cyfirma_actor.id,
            name=cyfirma_actor.name,
            description=cyfirma_actor.description,
            created=cyfirma_actor.created,
            modified=cyfirma_actor.modified,
            actor_types=[t.value for t in cyfirma_actor.threat_actor_types],
            primary_motivation=cyfirma_actor.primary_motivation.value if cyfirma_actor.primary_motivation else None,
            aliases=cyfirma_actor.aliases,
            origin_country=extension_props.get("origin-of-country"),
            target_countries=target_countries,
            target_industries=target_industries,
            target_technologies=target_technologies,
            confidence_score=confidence_score,
            severity_level=severity_level,
            tags=self._generate_tags(cyfirma_actor, extension_props)
        )
    
    def _parse_comma_separated(self, value: str) -> List[str]:
        """Parse comma-separated string into list"""
        if not value:
            return []
        return [item.strip() for item in value.split(",") if item.strip()]
    
    def _calculate_confidence_score(self, actor: CyfirmaThreatActor, extensions: Dict) -> float:
        """Calculate confidence score based on data completeness"""
        score = 0.0
        
        # Base score for having core data
        if actor.name: score += 0.2
        if actor.description: score += 0.2
        if actor.aliases: score += 0.1
        
        # Attribution data
        if extensions.get("origin-of-country"): score += 0.2
        if extensions.get("target-countries"): score += 0.1
        if extensions.get("target-industries"): score += 0.1
        
        # Technical details
        if extensions.get("target-technologies"): score += 0.1
        
        return min(score, 1.0)
    
    def _determine_severity_level(self, actor: CyfirmaThreatActor, target_industries: List[str]) -> str:
        """Determine severity level based on actor characteristics"""
        critical_industries = ["government", "defense", "military", "critical infrastructure", "energy"]
        high_risk_types = ["nation-state", "crime-syndicate"]
        
        # Check for critical targeting
        if any(industry.lower() in " ".join(target_industries).lower() for industry in critical_industries):
            return "critical"
        
        # Check actor type
        if any(actor_type in high_risk_types for actor_type in [t.value for t in actor.threat_actor_types]):
            return "high"
        
        # Check for multiple aliases (indicates sophistication)
        if len(actor.aliases) > 5:
            return "high"
        
        return "medium"
    
    def _generate_tags(self, actor: CyfirmaThreatActor, extensions: Dict) -> List[str]:
        """Generate tags for categorization and search"""
        tags = []
        
        # Add actor types as tags
        tags.extend([t.value for t in actor.threat_actor_types])
        
        # Add motivation as tag
        if actor.primary_motivation:
            tags.append(actor.primary_motivation.value)
        
        # Add origin country
        if extensions.get("origin-of-country"):
            tags.append(f"origin:{extensions['origin-of-country']}")
        
        # Add major target industries
        target_industries = self._parse_comma_separated(extensions.get("target-industries", ""))
        for industry in target_industries[:3]:  # Limit to top 3
            tags.append(f"targets:{industry.lower()}")
        
        return tags
    
    def _cache_data(self, key: str, data: Any):
        """Cache data with timestamp"""
        self.cache[key] = {
            "data": data,
            "timestamp": datetime.utcnow()
        }
    
    def _is_cache_valid(self, key: str) -> bool:
        """Check if cached data is still valid"""
        if key not in self.cache:
            return False
        
        cache_age = datetime.utcnow() - self.cache[key]["timestamp"]
        return cache_age < self.cache_ttl
