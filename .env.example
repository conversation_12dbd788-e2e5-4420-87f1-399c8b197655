# CTI Dashboard Environment Configuration
# Copy this file to .env and update with your actual values

# Application Settings
APP_NAME="CTI Dashboard"
APP_VERSION="1.0.0"
DEBUG=true
ENVIRONMENT=development

# API Configuration
API_HOST=127.0.0.1
API_PORT=8000
API_PREFIX=/api/v1

# Security Settings
SECRET_KEY=your-super-secret-key-here-must-be-at-least-32-characters-long
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# Database Configuration
DATABASE_URL=sqlite:///./cti_dashboard.db
# For PostgreSQL: postgresql://user:password@localhost/cti_dashboard
# For MySQL: mysql://user:password@localhost/cti_dashboard

# Redis Configuration (optional, for caching and sessions)
REDIS_URL=redis://localhost:6379/0

# External API Keys (configure via UI or set here)
CYFIRMA_API_KEY=your-cyfirma-api-key-here
VIRUSTOTAL_API_KEY=your-virustotal-api-key-here
SHODAN_API_KEY=your-shodan-api-key-here
CENSYS_API_ID=your-censys-api-id-here
CENSYS_API_SECRET=your-censys-api-secret-here
ZOOMEYE_API_KEY=your-zoomeye-api-key-here
ABUSEIPDB_API_KEY=your-abuseipdb-api-key-here

# LLM Configuration
LLM_SERVICE=openai
OPENAI_API_KEY=your-openai-api-key-here
DEEPSEEK_API_KEY=your-deepseek-api-key-here
OLLAMA_BASE_URL=http://localhost:11434

# Feature Flags
ENABLE_PASSIVE_SCANNING=true
ENABLE_WATCHLIST_MONITORING=true
ENABLE_AI_ANALYSIS=true
ENABLE_SUBNET_MATCHING=true

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Security Settings
SECURITY_REQUIRE_AUTHENTICATION=true
SECURITY_SESSION_TIMEOUT_MINUTES=480
SECURITY_MAX_LOGIN_ATTEMPTS=5
SECURITY_LOCKOUT_DURATION_MINUTES=15
SECURITY_ENABLE_RATE_LIMITING=true
SECURITY_ENABLE_REQUEST_LOGGING=true
SECURITY_ENABLE_CORS=true
SECURITY_ENCRYPT_SENSITIVE_DATA=true
SECURITY_DATA_RETENTION_DAYS=365

# Integration Settings
INTEGRATION_DEFAULT_TIMEOUT=30
INTEGRATION_CYFIRMA_TIMEOUT=45
INTEGRATION_VIRUSTOTAL_TIMEOUT=30
INTEGRATION_SHODAN_TIMEOUT=30
INTEGRATION_MAX_RETRIES=3
INTEGRATION_RETRY_DELAY=1.0
INTEGRATION_CACHE_TTL_HOURS=6
INTEGRATION_ENABLE_CACHING=true
INTEGRATION_MAX_BATCH_SIZE=100
INTEGRATION_BATCH_TIMEOUT=300
